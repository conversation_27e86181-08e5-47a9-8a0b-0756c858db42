# Moon-Break: Chronicles of the Ottoman Empire

A 2D MMORPG set in Ottoman Istanbul, built with Godot 4.

## Project Structure

```
moonbreak/
├── assets/                 # All game assets
│   ├── sprites/            # Sprite sheets and textures
│   │   ├── characters/     # Character sprites (64x64 base)
│   │   ├── environment/    # Tiles and backgrounds (32x32 tiles)
│   │   ├── ui/            # UI elements and icons
│   │   └── vfx/           # Visual effects and particles
│   ├── audio/             # Sound and music files
│   │   ├── bgm/           # Background music (Ney flute + Ottoman percussion)
│   │   └── sfx/           # Sound effects (scimitar, flames, etc.)
│   └── fonts/             # Ottoman-inspired fonts
├── scripts/               # All GDScript files
│   ├── core/              # Core game systems (singletons)
│   ├── characters/        # Character controllers and classes
│   ├── world/             # World management and environments
│   ├── combat/            # Combat system and mechanics
│   ├── ui/                # User interface scripts
│   └── network/           # Multiplayer networking
├── scenes/                # Godot scene files (.tscn)
│   ├── characters/        # Character scene templates
│   ├── world/             # World and environment scenes
│   ├── ui/                # UI scenes and menus
│   └── menus/             # Game menus and screens
└── resources/             # Godot resources (.tres)
    ├── materials/         # Shader materials
    ├── animations/        # Animation resources
    └── data/              # Game data (items, skills, etc.)
```

## Game Features

### Setting: Ottoman Istanbul
- **Sultanahmet District**: Historic Peninsula (L1-15)
- **Galata District**: European quarter (L15-30)
- **Topkapi Palace**: Imperial complex (L30-45)
- **Bosphorus Shores**: Waterfront fortresses (L45-60)

### Character Classes
- **Janissary**: Elite infantry with scimitar combat
- **Sipahi**: Cavalry with mounted combat
- **Sufi Mystic**: Spiritual warrior with mystical abilities

### Core Systems
- 8-directional movement with pixel-perfect rendering
- Ottoman-themed combat with stance system
- Ancient Relic system (breakable Byzantine/Ottoman artifacts)
- 4-player multiplayer with authoritative server
- Ottoman manuscript UI with Arabic calligraphy

## Development Status

See `DEVELOPMENT_PLAN.md` for detailed development roadmap and current progress.

## Getting Started

1. Install Godot 4.2+
2. Open `project.godot` in Godot Editor
3. Run the project to start development

## Art Direction

- **Style**: Hand-drawn, 32px base tileset
- **Palette**: 16-color palette per district
- **Architecture**: Ottoman/Byzantine buildings
- **UI**: Paper manuscript aesthetic with Islamic geometric patterns
