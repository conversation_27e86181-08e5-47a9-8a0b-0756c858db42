# Moon-Break: Chronicles of the Jade Empire - Development Plan

## Project Overview
**Genre**: 2D MMORPG (Metin2-inspired)
**Engine**: Godot 4
**Art Style**: Hand-drawn, 32px tileset, 16-color palette per biome
**Target**: M0 Prototype (1 zone, 1 class, 1 Metin, 4 players)

## Development Phases

### Phase 1: Project Foundation & Setup ⏳
**Status**: Not Started
**Estimated Time**: 1-2 days

#### Tasks:
- [ ] Initialize Godot 4 Project
- [ ] Set up Project Folder Structure
- [ ] Configure Project Settings (pixel-perfect rendering)
- [ ] Create Core Architecture Scripts (GameManager, SceneManager)
- [ ] Set up Version Control (git + .gitignore)

#### Deliverables:
- Clean project structure
- Core singleton systems
- Pixel-perfect rendering pipeline
- Version control setup

---

### Phase 2: Core Character System ⏳
**Status**: Not Started
**Estimated Time**: 3-4 days

#### Key Features:
- 8-directional movement (3-frame walk, 4-frame run, 2-frame idle)
- Base character controller
- Animation state machine
- Input handling system
- Character customization foundation (6 hairstyles, 4 face scars, 3 skin tones)

#### Technical Requirements:
- 64×64 sprite sheets
- Smooth movement with tile-based positioning
- Animation blending system

---

### Phase 3: World & Environment ⏳
**Status**: Not Started
**Estimated Time**: 4-5 days

#### Key Features:
- Village of So-Hwa zone implementation
- 32×32 tile system with 256×256 chunks
- 5-layer parallax system (sky, far mountain, mid forest, near ground, overlay rain)
- Seamless map streaming foundation
- Basic lighting and atmosphere

#### Art Assets Needed:
- village_ground.png (512×512)
- Parallax background layers
- Environmental decorations (hanok roofs, paper lanterns)

---

### Phase 4: Combat System Foundation ⏳
**Status**: Not Started
**Estimated Time**: 5-6 days

#### Key Features:
- Warrior class implementation (Tiger/Turtle/Dragon stances)
- 3-hit combo system for melee
- Hit-stop mechanics (4-frame freeze on critical, 2-frame screen shake)
- Basic damage calculation
- Attack animations and VFX

#### Technical Requirements:
- State machine for combat states
- Collision detection system
- Damage number display system
- VFX system for slash arcs (8-frame white to red gradient)

---

### Phase 5: Metin Stone System ⏳
**Status**: Not Started
**Estimated Time**: 3-4 days

#### Key Features:
- Breakable Metin stones (3 sizes: 100/300/1000 HP)
- Loot drop system (60% gold, 30% upgrade powder, 8% rare talisman, 2% costume ticket)
- Demon spawning on break (3/5/9 demons based on size)
- Basic AI for spawned demons

#### Technical Requirements:
- Destructible object system
- Loot table implementation
- Basic enemy AI with aggro system (6 tile radius, 12 tile leash)

---

### Phase 6: Networking Foundation ⏳
**Status**: Not Started
**Estimated Time**: 6-7 days

#### Key Features:
- Client-server architecture setup
- 4-player multiplayer support
- Authoritative server (20Hz tick rate)
- Basic anti-cheat (movement delta clamp 8px/tick)
- ENet packet compression

#### Technical Requirements:
- Headless server build
- Network synchronization for movement and combat
- Player state management
- Basic lag compensation

---

### Phase 7: UI System ⏳
**Status**: Not Started
**Estimated Time**: 4-5 days

#### Key Features:
- Paper-scroll inventory (10×6 grid with drag-drop)
- Hotbar system (8 slots + 3 stances)
- Minimap with Metin spawn indicators
- Health/mana bars
- Chat system foundation

#### Art Assets Needed:
- paper_scroll.9.png (64×64 sliceable)
- UI icons and elements
- Font setup (Noto Serif KR, Blood Rush 8×8 for damage numbers)

---

### Phase 8: M0 Prototype Polish ⏳
**Status**: Not Started
**Estimated Time**: 2-3 days

#### Key Features:
- Bug fixes and optimization
- Performance testing with 4 players
- Basic game balance
- Documentation and build preparation

---

## Technical Architecture

### Core Systems:
1. **GameManager**: Main game state, scene transitions
2. **NetworkManager**: Client-server communication
3. **PlayerManager**: Character data, inventory, stats
4. **WorldManager**: Zone loading, chunk streaming
5. **CombatManager**: Damage calculation, hit detection
6. **UIManager**: Interface state management

### Database Schema (Future):
- characters table
- items table
- guilds table
- market table
- chat_log table

## Asset Pipeline

### Sprites:
- Base: 64×64 character sheets
- Tiles: 32×32 environment tiles
- UI: Paper-scroll aesthetic with 9-slice scaling
- VFX: Additive blend effects

### Audio:
- BGM: Taeguk drum + bamboo flute (90 BPM)
- SFX: Metal scrapes, fire crackles, stone breaks

## Milestones

### M0 (Current Target):
- ✅ 1 zone (Village of So-Hwa)
- ✅ 1 class (Warrior)
- ✅ 1 Metin stone type
- ✅ 4-player multiplayer

### M3 (Future):
- 3 classes total
- PvP toggle system
- Guild creation

### M6 (Future):
- Sky Palace zone
- Guild war system
- Market stalls

### M9 (Future):
- Pet system
- Costume system
- Steam Workshop integration

---

## Current Status
**Phase**: 1 (Project Foundation & Setup)
**Next Task**: Initialize Godot 4 Project
**Overall Progress**: 0% (Just started)

## Notes
- Focus on M0 prototype first
- Keep systems simple and extensible
- Prioritize core gameplay loop
- Regular testing with multiplayer scenarios

---

## Progress Log
*This section will be updated as we complete tasks*

### [Date] - Phase 1 Started
- Created development plan
- Set up task management system
- Ready to begin Godot project initialization