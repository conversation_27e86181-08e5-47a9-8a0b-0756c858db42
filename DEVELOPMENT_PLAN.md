# Moon-Break: Chronicles of the Ottoman Empire - Development Plan

## Project Overview
**Genre**: 2D MMORPG (Metin2-inspired)
**Engine**: Godot 4
**Art Style**: Hand-drawn, 32px tileset, Ottoman/Byzantine architectural style, 16-color palette per district
**Setting**: Ottoman Empire at its peak (16th-17th century), various districts of Istanbul
**Target**: M0 Prototype (1 district, 1 class, 1 ancient relic, 4 players)

## Development Phases

### Phase 1: Project Foundation & Setup ⏳
**Status**: Not Started
**Estimated Time**: 1-2 days

#### Tasks:
- [ ] Initialize Godot 4 Project
- [ ] Set up Project Folder Structure
- [ ] Configure Project Settings (pixel-perfect rendering)
- [ ] Create Core Architecture Scripts (GameManager, SceneManager)
- [ ] Set up Version Control (git + .gitignore)

#### Deliverables:
- Clean project structure
- Core singleton systems
- Pixel-perfect rendering pipeline
- Version control setup

---

### Phase 2: Core Character System ⏳
**Status**: Not Started
**Estimated Time**: 3-4 days

#### Key Features:
- 8-directional movement (3-frame walk, 4-frame run, 2-frame idle)
- Base character controller
- Animation state machine
- Input handling system
- Character customization foundation (6 hairstyles, 4 face scars, 3 skin tones)

#### Technical Requirements:
- 64×64 sprite sheets
- Smooth movement with tile-based positioning
- Animation blending system

---

### Phase 3: World & Environment ⏳
**Status**: Not Started
**Estimated Time**: 4-5 days

#### Key Features:
- Sultanahmet District implementation (starting zone)
- 32×32 tile system with 256×256 chunks
- 5-layer parallax system (sky, Bosphorus, minarets, cobblestone streets, atmospheric fog)
- Seamless map streaming foundation
- Ottoman architectural lighting and atmosphere

#### Art Assets Needed:
- sultanahmet_cobblestone.png (512×512)
- Parallax background layers (Hagia Sophia, Blue Mosque silhouettes)
- Environmental decorations (Ottoman fountains, market stalls, hanging carpets)

---

### Phase 4: Combat System Foundation ⏳
**Status**: Not Started
**Estimated Time**: 5-6 days

#### Key Features:
- Janissary class implementation (Disciplined/Berserker/Guardian stances)
- 3-hit combo system for scimitar combat
- Hit-stop mechanics (4-frame freeze on critical, 2-frame screen shake)
- Basic damage calculation
- Ottoman-themed attack animations and VFX (crescent slashes, gunpowder effects)

#### Technical Requirements:
- State machine for combat states
- Collision detection system
- Damage number display system
- VFX system for slash arcs (8-frame white to red gradient)

---

### Phase 5: Metin Stone System ⏳
**Status**: Not Started
**Estimated Time**: 3-4 days

#### Key Features:
- Breakable Ancient Relics (3 sizes: 100/300/1000 HP) - Byzantine artifacts, Ottoman treasures
- Loot drop system (60% akçe coins, 30% upgrade gems, 8% rare Ottoman artifacts, 2% costume patterns)
- Cursed Guardian spawning on break (3/5/9 guardians based on size)
- Basic AI for spawned Byzantine/Ottoman spirits

#### Technical Requirements:
- Destructible object system
- Loot table implementation
- Basic enemy AI with aggro system (6 tile radius, 12 tile leash)

---

### Phase 6: Networking Foundation ⏳
**Status**: Not Started
**Estimated Time**: 6-7 days

#### Key Features:
- Client-server architecture setup
- 4-player multiplayer support
- Authoritative server (20Hz tick rate)
- Basic anti-cheat (movement delta clamp 8px/tick)
- ENet packet compression

#### Technical Requirements:
- Headless server build
- Network synchronization for movement and combat
- Player state management
- Basic lag compensation

---

### Phase 7: UI System ⏳
**Status**: Not Started
**Estimated Time**: 4-5 days

#### Key Features:
- Ottoman manuscript inventory (10×6 grid with drag-drop)
- Hotbar system (8 slots + 3 stances)
- Minimap with Ancient Relic spawn indicators
- Health/stamina bars with Ottoman geometric patterns
- Chat system foundation

#### Art Assets Needed:
- ottoman_manuscript.9.png (64×64 sliceable with Arabic calligraphy borders)
- UI icons with Ottoman/Islamic geometric patterns
- Font setup (Ottoman-inspired serif, Arabic numerals for damage numbers)

---

### Phase 8: M0 Prototype Polish ⏳
**Status**: Not Started
**Estimated Time**: 2-3 days

#### Key Features:
- Bug fixes and optimization
- Performance testing with 4 players
- Basic game balance
- Documentation and build preparation

---

## Ottoman Istanbul - World Structure

### Districts (Server-Side Zones):
1. **Sultanahmet District** (L1-15): Historic Peninsula
   - Hagia Sophia, Blue Mosque, Hippodrome
   - Cobblestone streets, Ottoman fountains
   - Starting area with guild registrar and bazaar

2. **Galata District** (L15-30): Across the Golden Horn
   - Galata Tower, European quarter
   - Narrow medieval streets, Byzantine walls
   - Genoese architecture mixed with Ottoman

3. **Topkapi Palace Grounds** (L30-45): Imperial Complex
   - Palace courtyards, imperial gardens
   - Harem quarters, treasury chambers
   - High-security area with elite guards

4. **Bosphorus Shores** (L45-60): Waterfront Fortresses
   - Rumeli Fortress, Anadolu Fortress
   - Naval battles, floating platforms
   - Final guild-war arena on the strait

### Character Classes & Abilities:
```
┌──────────────┐      ┌──────────────┐      ┌──────────────┐
│   JANISSARY  │      │    SIPAHI    │      │  SUFI MYSTIC │
├──────────────┤      ├──────────────┤      ├──────────────┤
│ Stances:     │      │ Cavalry Arts:│      │ Mystical Arts│
│ • Disciplined│      │ • Charge     │      │ • Whirling   │
│ • Berserker  │      │ • Archery    │      │ • Blessing   │
│ • Guardian   │      │ • Lance      │      │ • Curse      │
└──────────────┘      └──────────────┘      └──────────────┘
```

---

## Technical Architecture

### Core Systems:
1. **GameManager**: Main game state, scene transitions
2. **NetworkManager**: Client-server communication
3. **PlayerManager**: Character data, inventory, stats
4. **WorldManager**: Zone loading, chunk streaming
5. **CombatManager**: Damage calculation, hit detection
6. **UIManager**: Interface state management

### Database Schema (Future):
- characters table
- items table
- guilds table
- market table
- chat_log table

## Asset Pipeline

### Sprites:
- Base: 64×64 character sheets
- Tiles: 32×32 environment tiles
- UI: Paper-scroll aesthetic with 9-slice scaling
- VFX: Additive blend effects

### Audio:
- BGM: Ney flute + Ottoman percussion (90 BPM), call to prayer ambience
- SFX: Scimitar clashes, brazier flames, marble breaking, Bosphorus waves

## Milestones

### M0 (Current Target):
- ✅ 1 district (Sultanahmet - Historic Peninsula)
- ✅ 1 class (Janissary)
- ✅ 1 Ancient Relic type
- ✅ 4-player multiplayer

### M3 (Future):
- 3 classes total (Janissary, Sipahi Cavalry, Sufi Mystic)
- PvP toggle system (Ottoman vs Byzantine factions)
- Guild creation (Ottoman military units)

### M6 (Future):
- Galata District (across Golden Horn)
- Guild war system (Siege of Constantinople mechanics)
- Grand Bazaar trading system

### M9 (Future):
- Pet system (Ottoman war animals - horses, falcons)
- Costume system (Ottoman court dress, military uniforms)
- Steam Workshop integration (custom Ottoman patterns)

---

## Current Status
**Phase**: 1 (Project Foundation & Setup)
**Next Task**: Initialize Godot 4 Project (Ottoman Theme)
**Overall Progress**: 0% (Just started - Theme Updated to Ottoman Empire)

## Notes
- Focus on M0 prototype first
- Keep systems simple and extensible
- Prioritize core gameplay loop
- Regular testing with multiplayer scenarios

---

## Progress Log
*This section will be updated as we complete tasks*

### 2025-07-26 - Phase 1 Started & Ottoman Theme Applied
- Created development plan
- Set up task management system
- **MAJOR UPDATE**: Transformed theme from Jade Empire to Ottoman Empire
- Updated all zones to Istanbul districts (Sultanahmet, Galata, Topkapi, Bosphorus)
- Changed classes to Ottoman military (Janissary, Sipahi, Sufi Mystic)
- Updated art direction to Ottoman/Byzantine architectural style
- Ready to begin Godot project initialization with Ottoman theme