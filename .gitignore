# Godot 4+ specific ignores
.godot/
.import/

# Godot-specific ignores
*.tmp
*.translation
*.import

# Mono-specific ignores (if using C#)
.mono/
data_*/
mono_crash.*.json

# System/Editor-specific ignores
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE-specific ignores
.vscode/
.idea/
*.swp
*.swo
*~

# Build outputs
builds/
exports/
*.exe
*.pck
*.zip
*.dmg
*.app

# Logs and temporary files
*.log
*.tmp
*.temp
*.cache

# User-specific files
user://
*.user

# Backup files
*.bak
*.backup
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Development tools
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Asset source files (keep compiled versions only)
# Uncomment if you want to ignore source art files
# *.psd
# *.ai
# *.blend
# *.max
# *.maya

# Documentation builds
docs/_build/

# Testing
coverage/
.coverage
.pytest_cache/

# Moon-Break specific ignores
server_logs/
player_data/
screenshots/
recordings/

# Temporary development files
TODO.md
NOTES.md
dev_notes/
