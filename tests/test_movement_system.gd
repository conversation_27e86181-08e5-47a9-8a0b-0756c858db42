extends GutTest

## Test suite for MovementSystem class
## Tests 8-directional movement calculations, stance modifiers, and Ottoman-specific movement features

func test_direction_enum_values():
	"""Test that direction enum has correct values"""
	assert_eq(MovementSystem.Direction.DOWN, 0, "DOWN should be 0")
	assert_eq(MovementSystem.Direction.DOWN_RIGHT, 1, "DOWN_RIGHT should be 1")
	assert_eq(MovementSystem.Direction.RIGHT, 2, "RIGHT should be 2")
	assert_eq(MovementSystem.Direction.UP_RIGHT, 3, "UP_RIGHT should be 3")
	assert_eq(MovementSystem.Direction.UP, 4, "UP should be 4")
	assert_eq(MovementSystem.Direction.UP_LEFT, 5, "UP_LEFT should be 5")
	assert_eq(MovementSystem.Direction.LEFT, 6, "LEFT should be 6")
	assert_eq(MovementSystem.Direction.DOWN_LEFT, 7, "DOWN_LEFT should be 7")

func test_direction_vectors():
	"""Test that direction vectors are correct"""
	var vectors = MovementSystem.DIRECTION_VECTORS
	
	assert_eq(vectors[MovementSystem.Direction.DOWN], Vector2(0, 1), "DOWN vector should be (0, 1)")
	assert_eq(vectors[MovementSystem.Direction.RIGHT], Vector2(1, 0), "RIGHT vector should be (1, 0)")
	assert_eq(vectors[MovementSystem.Direction.UP], Vector2(0, -1), "UP vector should be (0, -1)")
	assert_eq(vectors[MovementSystem.Direction.LEFT], Vector2(-1, 0), "LEFT vector should be (-1, 0)")
	
	# Test diagonal vectors
	assert_eq(vectors[MovementSystem.Direction.DOWN_RIGHT], Vector2(1, 1), "DOWN_RIGHT should be (1, 1)")
	assert_eq(vectors[MovementSystem.Direction.UP_RIGHT], Vector2(1, -1), "UP_RIGHT should be (1, -1)")
	assert_eq(vectors[MovementSystem.Direction.UP_LEFT], Vector2(-1, -1), "UP_LEFT should be (-1, -1)")
	assert_eq(vectors[MovementSystem.Direction.DOWN_LEFT], Vector2(-1, 1), "DOWN_LEFT should be (-1, 1)")

func test_direction_names():
	"""Test that direction names are correct"""
	var names = MovementSystem.DIRECTION_NAMES
	
	assert_eq(names[MovementSystem.Direction.DOWN], "down", "DOWN name should be 'down'")
	assert_eq(names[MovementSystem.Direction.RIGHT], "right", "RIGHT name should be 'right'")
	assert_eq(names[MovementSystem.Direction.UP], "up", "UP name should be 'up'")
	assert_eq(names[MovementSystem.Direction.LEFT], "left", "LEFT name should be 'left'")
	assert_eq(names[MovementSystem.Direction.DOWN_RIGHT], "down_right", "DOWN_RIGHT name should be 'down_right'")

func test_get_direction_from_vector():
	"""Test converting vectors to direction enums"""
	# Test cardinal directions
	assert_eq(MovementSystem.get_direction_from_vector(Vector2.DOWN), MovementSystem.Direction.DOWN, "Should detect DOWN")
	assert_eq(MovementSystem.get_direction_from_vector(Vector2.RIGHT), MovementSystem.Direction.RIGHT, "Should detect RIGHT")
	assert_eq(MovementSystem.get_direction_from_vector(Vector2.UP), MovementSystem.Direction.UP, "Should detect UP")
	assert_eq(MovementSystem.get_direction_from_vector(Vector2.LEFT), MovementSystem.Direction.LEFT, "Should detect LEFT")
	
	# Test diagonal directions
	var down_right = Vector2(1, 1).normalized()
	assert_eq(MovementSystem.get_direction_from_vector(down_right), MovementSystem.Direction.DOWN_RIGHT, "Should detect DOWN_RIGHT")
	
	var up_left = Vector2(-1, -1).normalized()
	assert_eq(MovementSystem.get_direction_from_vector(up_left), MovementSystem.Direction.UP_LEFT, "Should detect UP_LEFT")
	
	# Test zero vector (should default to DOWN)
	assert_eq(MovementSystem.get_direction_from_vector(Vector2.ZERO), MovementSystem.Direction.DOWN, "Zero vector should default to DOWN")

func test_get_vector_from_direction():
	"""Test converting direction enums to normalized vectors"""
	var down_vector = MovementSystem.get_vector_from_direction(MovementSystem.Direction.DOWN)
	assert_eq(down_vector, Vector2(0, 1), "DOWN should return (0, 1)")
	
	var right_vector = MovementSystem.get_vector_from_direction(MovementSystem.Direction.RIGHT)
	assert_eq(right_vector, Vector2(1, 0), "RIGHT should return (1, 0)")
	
	var down_right_vector = MovementSystem.get_vector_from_direction(MovementSystem.Direction.DOWN_RIGHT)
	var expected_diagonal = Vector2(1, 1).normalized()
	assert_almost_eq(down_right_vector.x, expected_diagonal.x, 0.01, "DOWN_RIGHT X should be normalized")
	assert_almost_eq(down_right_vector.y, expected_diagonal.y, 0.01, "DOWN_RIGHT Y should be normalized")

func test_get_direction_name():
	"""Test getting string names for directions"""
	assert_eq(MovementSystem.get_direction_name(MovementSystem.Direction.DOWN), "down", "Should return 'down'")
	assert_eq(MovementSystem.get_direction_name(MovementSystem.Direction.UP_RIGHT), "up_right", "Should return 'up_right'")
	assert_eq(MovementSystem.get_direction_name(MovementSystem.Direction.LEFT), "left", "Should return 'left'")

func test_get_opposite_direction():
	"""Test getting opposite directions"""
	assert_eq(MovementSystem.get_opposite_direction(MovementSystem.Direction.DOWN), MovementSystem.Direction.UP, "Opposite of DOWN should be UP")
	assert_eq(MovementSystem.get_opposite_direction(MovementSystem.Direction.RIGHT), MovementSystem.Direction.LEFT, "Opposite of RIGHT should be LEFT")
	assert_eq(MovementSystem.get_opposite_direction(MovementSystem.Direction.DOWN_RIGHT), MovementSystem.Direction.UP_LEFT, "Opposite of DOWN_RIGHT should be UP_LEFT")
	assert_eq(MovementSystem.get_opposite_direction(MovementSystem.Direction.UP_LEFT), MovementSystem.Direction.DOWN_RIGHT, "Opposite of UP_LEFT should be DOWN_RIGHT")

func test_is_diagonal_direction():
	"""Test diagonal direction detection"""
	# Cardinal directions should not be diagonal
	assert_false(MovementSystem.is_diagonal_direction(MovementSystem.Direction.DOWN), "DOWN should not be diagonal")
	assert_false(MovementSystem.is_diagonal_direction(MovementSystem.Direction.RIGHT), "RIGHT should not be diagonal")
	assert_false(MovementSystem.is_diagonal_direction(MovementSystem.Direction.UP), "UP should not be diagonal")
	assert_false(MovementSystem.is_diagonal_direction(MovementSystem.Direction.LEFT), "LEFT should not be diagonal")
	
	# Diagonal directions should be diagonal
	assert_true(MovementSystem.is_diagonal_direction(MovementSystem.Direction.DOWN_RIGHT), "DOWN_RIGHT should be diagonal")
	assert_true(MovementSystem.is_diagonal_direction(MovementSystem.Direction.UP_RIGHT), "UP_RIGHT should be diagonal")
	assert_true(MovementSystem.is_diagonal_direction(MovementSystem.Direction.UP_LEFT), "UP_LEFT should be diagonal")
	assert_true(MovementSystem.is_diagonal_direction(MovementSystem.Direction.DOWN_LEFT), "DOWN_LEFT should be diagonal")

func test_get_cardinal_directions():
	"""Test getting cardinal components of diagonal directions"""
	# Cardinal directions should return themselves
	var down_cardinals = MovementSystem.get_cardinal_directions(MovementSystem.Direction.DOWN)
	assert_eq(down_cardinals.size(), 1, "Cardinal direction should return single element")
	assert_eq(down_cardinals[0], MovementSystem.Direction.DOWN, "Should return itself")
	
	# Diagonal directions should return two cardinals
	var down_right_cardinals = MovementSystem.get_cardinal_directions(MovementSystem.Direction.DOWN_RIGHT)
	assert_eq(down_right_cardinals.size(), 2, "Diagonal should return two cardinals")
	assert_true(MovementSystem.Direction.DOWN in down_right_cardinals, "Should contain DOWN")
	assert_true(MovementSystem.Direction.RIGHT in down_right_cardinals, "Should contain RIGHT")
	
	var up_left_cardinals = MovementSystem.get_cardinal_directions(MovementSystem.Direction.UP_LEFT)
	assert_eq(up_left_cardinals.size(), 2, "Diagonal should return two cardinals")
	assert_true(MovementSystem.Direction.UP in up_left_cardinals, "Should contain UP")
	assert_true(MovementSystem.Direction.LEFT in up_left_cardinals, "Should contain LEFT")

func test_calculate_movement_input():
	"""Test movement input calculation (requires input simulation)"""
	# This test would require input simulation, which is complex in Godot
	# For now, we test that the function exists and returns a Vector2
	var input = MovementSystem.calculate_movement_input()
	assert_true(input is Vector2, "Should return Vector2")

func test_apply_movement_smoothing():
	"""Test movement smoothing calculations"""
	var current_velocity = Vector2(50, 0)
	var target_velocity = Vector2(100, 0)
	var acceleration = 200.0
	var deceleration = 150.0
	var delta = 0.1
	
	# Test acceleration
	var result = MovementSystem.apply_movement_smoothing(current_velocity, target_velocity, acceleration, deceleration, delta)
	assert_gt(result.x, current_velocity.x, "Should accelerate towards target")
	assert_le(result.x, target_velocity.x, "Should not exceed target")
	
	# Test deceleration
	current_velocity = Vector2(100, 0)
	target_velocity = Vector2.ZERO
	result = MovementSystem.apply_movement_smoothing(current_velocity, target_velocity, acceleration, deceleration, delta)
	assert_lt(result.x, current_velocity.x, "Should decelerate towards zero")
	assert_ge(result.x, 0, "Should not go below zero when decelerating")

func test_stance_movement_modifier():
	"""Test Ottoman stance movement speed modifiers"""
	var base_speed = 120.0
	
	# Test disciplined stance (no modifier)
	var disciplined_speed = MovementSystem.apply_stance_movement_modifier(base_speed, "disciplined")
	assert_eq(disciplined_speed, base_speed, "Disciplined stance should have no modifier")
	
	# Test charge stance (20% boost)
	var charge_speed = MovementSystem.apply_stance_movement_modifier(base_speed, "charge")
	assert_almost_eq(charge_speed, base_speed * 1.2, 0.1, "Charge stance should have 20% speed boost")
	
	# Test whirling stance (10% boost)
	var whirling_speed = MovementSystem.apply_stance_movement_modifier(base_speed, "whirling")
	assert_almost_eq(whirling_speed, base_speed * 1.1, 0.1, "Whirling stance should have 10% speed boost")
	
	# Test unknown stance (no modifier)
	var unknown_speed = MovementSystem.apply_stance_movement_modifier(base_speed, "unknown")
	assert_eq(unknown_speed, base_speed, "Unknown stance should have no modifier")

func test_stamina_cost_calculation():
	"""Test stamina cost calculations for different states"""
	# Test not running
	var cost = MovementSystem.calculate_stamina_cost(false, false, "disciplined")
	assert_eq(cost, 0.0, "Should have no cost when not running")
	
	# Test running with disciplined stance
	cost = MovementSystem.calculate_stamina_cost(true, false, "disciplined")
	assert_eq(cost, 30.0, "Should have base running cost")
	
	# Test running with charge stance (50% more)
	cost = MovementSystem.calculate_stamina_cost(true, false, "charge")
	assert_eq(cost, 45.0, "Charge stance should cost 50% more stamina")
	
	# Test running with whirling stance (20% less)
	cost = MovementSystem.calculate_stamina_cost(true, false, "whirling")
	assert_eq(cost, 24.0, "Whirling stance should cost 20% less stamina")
	
	# Test running in combat (additional 10 cost)
	cost = MovementSystem.calculate_stamina_cost(true, true, "disciplined")
	assert_eq(cost, 40.0, "Should have additional cost in combat")

func test_animation_name_generation():
	"""Test animation name generation for directions and states"""
	# Test idle animation
	var anim_name = MovementSystem.get_animation_name_for_direction(MovementSystem.Direction.DOWN, false, false)
	assert_eq(anim_name, "idle_down", "Should generate idle_down animation name")
	
	# Test walking animation
	anim_name = MovementSystem.get_animation_name_for_direction(MovementSystem.Direction.RIGHT, true, false)
	assert_eq(anim_name, "walk_right", "Should generate walk_right animation name")
	
	# Test running animation
	anim_name = MovementSystem.get_animation_name_for_direction(MovementSystem.Direction.UP_LEFT, true, true)
	assert_eq(anim_name, "run_up_left", "Should generate run_up_left animation name")

func test_sprite_flip_settings():
	"""Test sprite flip settings for different directions"""
	# Test left-facing directions (should flip horizontally)
	var flip_settings = MovementSystem.get_sprite_flip_for_direction(MovementSystem.Direction.LEFT)
	assert_true(flip_settings.flip_h, "LEFT should flip horizontally")
	assert_false(flip_settings.flip_v, "LEFT should not flip vertically")
	
	flip_settings = MovementSystem.get_sprite_flip_for_direction(MovementSystem.Direction.UP_LEFT)
	assert_true(flip_settings.flip_h, "UP_LEFT should flip horizontally")
	
	flip_settings = MovementSystem.get_sprite_flip_for_direction(MovementSystem.Direction.DOWN_LEFT)
	assert_true(flip_settings.flip_h, "DOWN_LEFT should flip horizontally")
	
	# Test right-facing directions (should not flip horizontally)
	flip_settings = MovementSystem.get_sprite_flip_for_direction(MovementSystem.Direction.RIGHT)
	assert_false(flip_settings.flip_h, "RIGHT should not flip horizontally")
	
	flip_settings = MovementSystem.get_sprite_flip_for_direction(MovementSystem.Direction.UP_RIGHT)
	assert_false(flip_settings.flip_h, "UP_RIGHT should not flip horizontally")
	
	flip_settings = MovementSystem.get_sprite_flip_for_direction(MovementSystem.Direction.DOWN_RIGHT)
	assert_false(flip_settings.flip_h, "DOWN_RIGHT should not flip horizontally")

func test_movement_validation():
	"""Test movement direction validation against collisions"""
	# Create a mock CharacterBody2D for testing
	var character = CharacterBody2D.new()
	character.collision_layer = 1
	character.collision_mask = 4
	add_child_autofree(character)
	
	# Test movement validation (simplified - full test would require collision setup)
	var direction = Vector2.RIGHT
	var distance = 32.0
	
	var validated_direction = MovementSystem.validate_movement_direction(character, direction, distance)
	assert_true(validated_direction is Vector2, "Should return Vector2")
	assert_le(validated_direction.length(), 1.1, "Should return normalized or zero vector")

func test_debug_info_generation():
	"""Test movement debug information generation"""
	var character = CharacterBody2D.new()
	character.global_position = Vector2(100, 200)
	character.velocity = Vector2(50, -30)
	add_child_autofree(character)
	
	var direction = Vector2.UP_RIGHT.normalized()
	var debug_info = MovementSystem.get_movement_debug_info(character, direction)
	
	assert_true(debug_info is Dictionary, "Should return dictionary")
	assert_eq(debug_info["position"], Vector2(100, 200), "Should include position")
	assert_eq(debug_info["velocity"], Vector2(50, -30), "Should include velocity")
	assert_eq(debug_info["direction"], direction, "Should include direction")
	assert_true(debug_info.has("direction_enum"), "Should include direction enum")
	assert_true(debug_info.has("direction_name"), "Should include direction name")
	assert_true(debug_info.has("speed"), "Should include speed")
	assert_true(debug_info.has("is_moving"), "Should include movement state")

func test_vector_normalization_consistency():
	"""Test that all direction vectors maintain consistent normalization"""
	for i in range(8):
		var direction = i as MovementSystem.Direction
		var vector = MovementSystem.get_vector_from_direction(direction)
		
		# All vectors should be normalized (length = 1.0)
		assert_almost_eq(vector.length(), 1.0, 0.01, "Direction " + str(i) + " should be normalized")

func test_direction_conversion_roundtrip():
	"""Test that direction to vector to direction conversion is consistent"""
	for i in range(8):
		var original_direction = i as MovementSystem.Direction
		var vector = MovementSystem.get_vector_from_direction(original_direction)
		var converted_direction = MovementSystem.get_direction_from_vector(vector)
		
		assert_eq(converted_direction, original_direction, "Direction " + str(i) + " should convert consistently")
