extends GutTest

## Test suite for BaseCharacter class
## Tests movement, combat, health/stamina systems, and Ottoman-specific features

var base_character: BaseCharacter
var mock_sprite: Sprite2D
var mock_animation_player: AnimationPlayer
var mock_collision_shape: CollisionShape2D

func before_each():
	"""Set up test environment before each test"""
	# Create BaseCharacter instance
	base_character = BaseCharacter.new()
	
	# Create mock child nodes
	mock_sprite = Sprite2D.new()
	mock_sprite.name = "Sprite2D"
	base_character.add_child(mock_sprite)
	
	mock_animation_player = AnimationPlayer.new()
	mock_animation_player.name = "AnimationPlayer"
	base_character.add_child(mock_animation_player)
	
	mock_collision_shape = CollisionShape2D.new()
	mock_collision_shape.name = "CollisionShape2D"
	var shape = CapsuleShape2D.new()
	shape.radius = 16
	shape.height = 48
	mock_collision_shape.shape = shape
	base_character.add_child(mock_collision_shape)
	
	# Add to scene tree for proper initialization
	add_child_autofree(base_character)
	
	# Wait for ready
	await wait_frames(1)

func after_each():
	"""Clean up after each test"""
	if base_character and is_instance_valid(base_character):
		base_character.queue_free()

func test_character_initialization():
	"""Test that BaseCharacter initializes with correct default values"""
	assert_eq(base_character.character_name, "Ottoman Warrior", "Should have default name")
	assert_eq(base_character.character_class, PlayerManager.CharacterClass.JANISSARY, "Should default to Janissary")
	assert_eq(base_character.level, 1, "Should start at level 1")
	assert_eq(base_character.health, 100, "Should have 100 health")
	assert_eq(base_character.max_health, 100, "Should have 100 max health")
	assert_eq(base_character.stamina, 100, "Should have 100 stamina")
	assert_eq(base_character.max_stamina, 100, "Should have 100 max stamina")
	assert_eq(base_character.base_speed, 120.0, "Should have base speed of 120")
	assert_eq(base_character.facing_direction, Vector2.DOWN, "Should face down by default")
	assert_false(base_character.is_moving, "Should not be moving initially")
	assert_false(base_character.is_running, "Should not be running initially")
	assert_true(base_character.can_move, "Should be able to move initially")

func test_movement_direction_handling():
	"""Test movement direction calculations and updates"""
	# Test setting movement direction
	base_character.movement_direction = Vector2.RIGHT
	base_character.facing_direction = Vector2.RIGHT
	
	assert_eq(base_character.movement_direction, Vector2.RIGHT, "Movement direction should be set")
	assert_eq(base_character.facing_direction, Vector2.RIGHT, "Facing direction should be set")
	
	# Test diagonal movement normalization
	var diagonal = Vector2(1, 1).normalized()
	base_character.movement_direction = diagonal
	base_character.facing_direction = diagonal
	
	assert_almost_eq(base_character.movement_direction.length(), 1.0, 0.01, "Diagonal movement should be normalized")

func test_health_system():
	"""Test health management and damage/healing"""
	var initial_health = base_character.health
	
	# Test taking damage
	var died = base_character.take_damage(30)
	assert_eq(base_character.health, 70, "Should lose 30 health")
	assert_false(died, "Should not die from 30 damage")
	
	# Test healing
	base_character.heal(20)
	assert_eq(base_character.health, 90, "Should gain 20 health")
	
	# Test healing beyond max
	base_character.heal(50)
	assert_eq(base_character.health, base_character.max_health, "Should not exceed max health")
	
	# Test fatal damage
	died = base_character.take_damage(150)
	assert_eq(base_character.health, 0, "Should have 0 health after fatal damage")
	assert_true(died, "Should return true when character dies")
	assert_false(base_character.can_move, "Should not be able to move when dead")

func test_stamina_system():
	"""Test stamina consumption and regeneration"""
	# Test stamina consumption while running
	base_character.is_running = true
	base_character.is_in_combat = false
	base_character.current_stance = "disciplined"
	
	var initial_stamina = base_character.stamina
	base_character._consume_stamina(1.0)  # 1 second of running
	
	assert_lt(base_character.stamina, initial_stamina, "Stamina should decrease while running")
	
	# Test stamina regeneration
	base_character.is_running = false
	var stamina_before_regen = base_character.stamina
	base_character._regenerate_stamina(1.0)  # 1 second of regeneration
	
	assert_gt(base_character.stamina, stamina_before_regen, "Stamina should regenerate when not running")
	
	# Test stamina doesn't exceed max
	base_character.stamina = base_character.max_stamina - 5
	base_character._regenerate_stamina(1.0)
	assert_eq(base_character.stamina, base_character.max_stamina, "Stamina should not exceed maximum")

func test_combat_system():
	"""Test combat damage calculation and defense"""
	var attacker = BaseCharacter.new()
	attacker.attack_power = 20
	add_child_autofree(attacker)
	
	# Test damage with defense
	base_character.defense = 5
	var initial_health = base_character.health
	base_character.take_damage(20, attacker)
	
	# Damage should be reduced by defense (20 - 5 = 15, but minimum 1)
	var expected_damage = max(1, 20 - 5)
	assert_eq(base_character.health, initial_health - expected_damage, "Damage should be reduced by defense")

func test_stance_system():
	"""Test combat stance changes and effects"""
	# Test changing to disciplined stance
	base_character.change_stance("disciplined")
	assert_eq(base_character.current_stance, "disciplined", "Should change to disciplined stance")
	
	# Test changing to charge stance
	base_character.change_stance("charge")
	assert_eq(base_character.current_stance, "charge", "Should change to charge stance")
	
	# Test changing to whirling stance
	base_character.change_stance("whirling")
	assert_eq(base_character.current_stance, "whirling", "Should change to whirling stance")

func test_player_controlled_flag():
	"""Test player controlled functionality"""
	assert_false(base_character.is_player_controlled, "Should not be player controlled by default")
	
	base_character.set_player_controlled(true)
	assert_true(base_character.is_player_controlled, "Should be player controlled after setting")
	
	base_character.set_player_controlled(false)
	assert_false(base_character.is_player_controlled, "Should not be player controlled after unsetting")

func test_character_data_export():
	"""Test character data export functionality"""
	base_character.character_name = "Test Character"
	base_character.level = 5
	base_character.health = 80
	base_character.global_position = Vector2(100, 200)
	base_character.facing_direction = Vector2.UP
	base_character.is_moving = true
	base_character.current_stance = "charge"
	
	var data = base_character.get_character_data()
	
	assert_eq(data["name"], "Test Character", "Should export character name")
	assert_eq(data["level"], 5, "Should export level")
	assert_eq(data["health"], 80, "Should export health")
	assert_eq(data["position"], Vector2(100, 200), "Should export position")
	assert_eq(data["facing_direction"], Vector2.UP, "Should export facing direction")
	assert_eq(data["is_moving"], true, "Should export movement state")
	assert_eq(data["stance"], "charge", "Should export stance")

func test_position_and_direction_setting():
	"""Test network synchronization position/direction setting"""
	var new_pos = Vector2(300, 400)
	var new_direction = Vector2.LEFT
	
	base_character.set_position_and_direction(new_pos, new_direction)
	
	assert_eq(base_character.global_position, new_pos, "Should set position")
	assert_eq(base_character.facing_direction, new_direction, "Should set facing direction")

func test_collision_setup():
	"""Test collision layer and mask setup"""
	assert_eq(base_character.collision_layer, 1, "Should be on player collision layer (1)")
	assert_eq(base_character.collision_mask, 4, "Should collide with environment layer (4)")

func test_movement_state_signals():
	"""Test that movement state changes emit proper signals"""
	var signal_emitted = false
	var emitted_is_moving = false
	var emitted_is_running = false
	
	base_character.movement_state_changed.connect(func(is_moving, is_running):
		signal_emitted = true
		emitted_is_moving = is_moving
		emitted_is_running = is_running
	)
	
	# Simulate movement state change
	base_character.is_moving = false
	base_character.velocity = Vector2(100, 0)  # Set velocity to trigger movement state
	base_character._update_movement_state()
	
	await wait_frames(1)
	
	assert_true(signal_emitted, "Movement state changed signal should be emitted")

func test_health_stamina_signals():
	"""Test that health and stamina changes emit signals"""
	var health_signal_emitted = false
	var stamina_signal_emitted = false
	
	base_character.health_changed.connect(func(new_health, max_health):
		health_signal_emitted = true
	)
	
	base_character.stamina_changed.connect(func(new_stamina, max_stamina):
		stamina_signal_emitted = true
	)
	
	# Trigger health change
	base_character.take_damage(10)
	assert_true(health_signal_emitted, "Health changed signal should be emitted")
	
	# Trigger stamina change
	base_character.is_running = true
	base_character._consume_stamina(0.1)
	assert_true(stamina_signal_emitted, "Stamina changed signal should be emitted")

func test_death_handling():
	"""Test character death behavior"""
	# Kill the character
	base_character.take_damage(200)
	
	assert_eq(base_character.health, 0, "Health should be 0")
	assert_false(base_character.can_move, "Should not be able to move when dead")
	
	# Test that movement is disabled
	base_character.movement_direction = Vector2.RIGHT
	base_character._apply_movement(1.0)
	assert_eq(base_character.velocity, Vector2.ZERO, "Velocity should be zero when dead")
