extends GutTest

## Test suite for Janissary class
## Tests Ottoman elite infantry specific features, formation bonuses, and combat abilities

var janissary: Janissary
var mock_sprite: Sprite2D
var mock_animation_player: AnimationPlayer
var mock_collision_shape: CollisionShape2D

func before_each():
	"""Set up test environment before each test"""
	# Create Janissary instance
	janissary = Janissary.new()
	
	# Create mock child nodes
	mock_sprite = Sprite2D.new()
	mock_sprite.name = "Sprite2D"
	janissary.add_child(mock_sprite)
	
	mock_animation_player = AnimationPlayer.new()
	mock_animation_player.name = "AnimationPlayer"
	janissary.add_child(mock_animation_player)
	
	mock_collision_shape = CollisionShape2D.new()
	mock_collision_shape.name = "CollisionShape2D"
	var shape = CapsuleShape2D.new()
	shape.radius = 16
	shape.height = 48
	mock_collision_shape.shape = shape
	janissary.add_child(mock_collision_shape)
	
	# Add to scene tree
	add_child_autofree(janissary)
	
	# Wait for ready
	await wait_frames(1)

func after_each():
	"""Clean up after each test"""
	if janissary and is_instance_valid(janissary):
		janissary.queue_free()

func test_janissary_initialization():
	"""Test Janissary class initialization with Ottoman-specific values"""
	assert_eq(janissary.character_name, "Janissary Warrior", "Should have Janissary-specific name")
	assert_eq(janissary.character_class, PlayerManager.CharacterClass.JANISSARY, "Should be Janissary class")
	assert_eq(janissary.current_stance, "disciplined", "Should default to disciplined stance")
	assert_eq(janissary.max_health, 120, "Should have enhanced health")
	assert_eq(janissary.attack_power, 12, "Should have enhanced attack power")
	assert_eq(janissary.defense, 8, "Should have good defense")
	assert_eq(janissary.scimitar_proficiency, 10, "Should have scimitar proficiency")
	assert_eq(janissary.gunpowder_knowledge, 5, "Should have gunpowder knowledge")
	assert_eq(janissary.formation_training, 8, "Should have formation training")
	assert_false(janissary.formation_bonus_active, "Should not have formation bonus initially")

func test_disciplined_stance_bonuses():
	"""Test disciplined stance provides proper bonuses"""
	var base_defense = 8  # Base Janissary defense
	var disciplined_bonus = 5  # Disciplined stance defense bonus
	
	# Ensure disciplined stance is active
	janissary.current_stance = "disciplined"
	janissary._apply_disciplined_stance_bonuses()
	
	assert_eq(janissary.defense, base_defense + disciplined_bonus, "Should have defense bonus from disciplined stance")

func test_scimitar_attack():
	"""Test Janissary scimitar attack functionality"""
	janissary.player_id = 1
	janissary.can_attack = true
	janissary.attack_cooldown = 0.0
	janissary.facing_direction = Vector2.RIGHT
	janissary.global_position = Vector2(100, 100)
	janissary.current_stance = "disciplined"
	
	var signal_emitted = false
	var emitted_action = ""
	
	janissary.combat_action_performed.connect(func(action_type):
		signal_emitted = true
		emitted_action = action_type
	)
	
	janissary._perform_scimitar_attack()
	
	assert_false(janissary.can_attack, "Should not be able to attack immediately after scimitar attack")
	assert_gt(janissary.attack_cooldown, 0.0, "Should have attack cooldown")
	assert_true(signal_emitted, "Should emit combat action signal")
	assert_eq(emitted_action, "scimitar_attack", "Should emit scimitar attack action")

func test_formation_bonus_detection():
	"""Test formation bonus activation with nearby Janissaries"""
	# Initially no formation bonus
	assert_false(janissary.formation_bonus_active, "Should not have formation bonus initially")
	
	# Test finding nearby Janissaries (would require PlayerManager mock for full test)
	var nearby_janissaries = janissary._find_nearby_janissaries()
	assert_true(nearby_janissaries is Array, "Should return array of nearby Janissaries")

func test_formation_bonus_effects():
	"""Test formation bonus stat modifications"""
	var base_attack = janissary.attack_power
	var base_defense = janissary.defense
	var base_speed = janissary.base_speed
	
	# Activate formation bonus
	janissary._apply_formation_bonus(true)
	
	assert_eq(janissary.attack_power, base_attack + 2, "Should gain +2 attack from formation")
	assert_eq(janissary.defense, base_defense + 3, "Should gain +3 defense from formation")
	assert_almost_eq(janissary.base_speed, base_speed * 1.1, 0.1, "Should gain 10% speed from formation")
	assert_true(janissary.formation_bonus_active, "Formation bonus should be active")
	
	# Deactivate formation bonus
	janissary._apply_formation_bonus(false)
	
	assert_eq(janissary.attack_power, base_attack, "Should return to base attack")
	assert_eq(janissary.defense, base_defense, "Should return to base defense")
	assert_almost_eq(janissary.base_speed, base_speed, 0.1, "Should return to base speed")
	assert_false(janissary.formation_bonus_active, "Formation bonus should be inactive")

func test_formation_attack():
	"""Test special formation attack ability"""
	janissary.player_id = 1
	janissary.can_attack = true
	janissary.formation_bonus_active = true
	janissary.facing_direction = Vector2.DOWN
	janissary.global_position = Vector2(200, 200)
	
	var signal_emitted = false
	var emitted_action = ""
	
	janissary.combat_action_performed.connect(func(action_type):
		signal_emitted = true
		emitted_action = action_type
	)
	
	janissary.perform_disciplined_formation_attack()
	
	assert_false(janissary.can_attack, "Should not be able to attack after formation attack")
	assert_almost_eq(janissary.attack_cooldown, 0.8, 0.1, "Should have reduced cooldown in formation")
	assert_true(signal_emitted, "Should emit combat action signal")
	assert_eq(emitted_action, "formation_attack", "Should emit formation attack action")

func test_formation_attack_requires_formation():
	"""Test that formation attack requires active formation bonus"""
	janissary.can_attack = true
	janissary.formation_bonus_active = false
	
	# Should not be able to perform formation attack without formation bonus
	janissary.perform_disciplined_formation_attack()
	
	# Attack state should remain unchanged since formation attack wasn't performed
	assert_true(janissary.can_attack, "Should still be able to attack if formation attack failed")

func test_gunpowder_weapon():
	"""Test Janissary gunpowder weapon special ability"""
	janissary.stamina = 50
	janissary.facing_direction = Vector2.UP
	janissary.global_position = Vector2(300, 300)
	
	var signal_emitted = false
	var emitted_action = ""
	
	janissary.combat_action_performed.connect(func(action_type):
		signal_emitted = true
		emitted_action = action_type
	)
	
	janissary.use_gunpowder_weapon()
	
	assert_eq(janissary.stamina, 20, "Should consume 30 stamina for gunpowder weapon")
	assert_false(janissary.can_attack, "Should not be able to attack after gunpowder shot")
	assert_eq(janissary.attack_cooldown, 3.0, "Should have 3 second reload time")
	assert_true(signal_emitted, "Should emit combat action signal")
	assert_eq(emitted_action, "gunpowder_shot", "Should emit gunpowder shot action")

func test_gunpowder_weapon_stamina_requirement():
	"""Test gunpowder weapon requires sufficient stamina"""
	janissary.stamina = 20  # Less than required 30
	var initial_stamina = janissary.stamina
	
	janissary.use_gunpowder_weapon()
	
	assert_eq(janissary.stamina, initial_stamina, "Should not consume stamina if insufficient")
	assert_true(janissary.can_attack, "Should still be able to attack if gunpowder shot failed")

func test_stance_change_with_disciplined_bonuses():
	"""Test stance changes properly handle disciplined bonuses"""
	var base_defense = janissary.defense
	
	# Change away from disciplined stance
	janissary._change_stance("charge")
	
	assert_eq(janissary.current_stance, "charge", "Should change to charge stance")
	# Defense should be reduced when leaving disciplined stance
	
	# Change back to disciplined stance
	janissary._change_stance("disciplined")
	
	assert_eq(janissary.current_stance, "disciplined", "Should change back to disciplined stance")

func test_disciplined_stance_signal():
	"""Test disciplined stance change emits proper signal"""
	var signal_emitted = false
	var emitted_bonus_active = false
	
	janissary.disciplined_stance_changed.connect(func(bonus_active):
		signal_emitted = true
		emitted_bonus_active = bonus_active
	)
	
	# Change to disciplined stance
	janissary._change_stance("disciplined")
	
	assert_true(signal_emitted, "Should emit disciplined stance changed signal")
	assert_true(emitted_bonus_active, "Should indicate disciplined bonus is active")
	
	# Reset signal tracking
	signal_emitted = false
	
	# Change away from disciplined stance
	janissary._change_stance("charge")
	
	assert_true(signal_emitted, "Should emit signal when leaving disciplined stance")
	assert_false(emitted_bonus_active, "Should indicate disciplined bonus is inactive")

func test_level_up_system():
	"""Test Janissary level up bonuses"""
	var initial_level = janissary.level
	var initial_health = janissary.max_health
	var initial_stamina = janissary.max_stamina
	var initial_attack = janissary.attack_power
	var initial_defense = janissary.defense
	var initial_scimitar = janissary.scimitar_proficiency
	var initial_discipline = janissary.discipline_level
	
	janissary.level_up()
	
	assert_eq(janissary.level, initial_level + 1, "Should increase level")
	assert_eq(janissary.discipline_level, initial_discipline + 1, "Should increase discipline level")
	assert_eq(janissary.max_health, initial_health + 8, "Should gain 8 max health")
	assert_eq(janissary.max_stamina, initial_stamina + 5, "Should gain 5 max stamina")
	assert_eq(janissary.attack_power, initial_attack + 2, "Should gain 2 attack power")
	assert_eq(janissary.defense, initial_defense + 2, "Should gain 2 defense")
	assert_eq(janissary.scimitar_proficiency, initial_scimitar + 1, "Should gain 1 scimitar proficiency")
	assert_eq(janissary.health, janissary.max_health, "Should restore full health")
	assert_eq(janissary.stamina, janissary.max_stamina, "Should restore full stamina")

func test_level_up_proficiency_bonuses():
	"""Test that every 3 levels increases additional proficiencies"""
	janissary.level = 2
	var initial_gunpowder = janissary.gunpowder_knowledge
	var initial_formation = janissary.formation_training
	
	# Level up to 3 (divisible by 3)
	janissary.level_up()
	
	assert_eq(janissary.gunpowder_knowledge, initial_gunpowder + 1, "Should gain gunpowder knowledge at level 3")
	assert_eq(janissary.formation_training, initial_formation + 1, "Should gain formation training at level 3")

func test_janissary_info_export():
	"""Test Janissary-specific information export"""
	janissary.player_id = 99
	janissary.discipline_level = 5
	janissary.scimitar_proficiency = 15
	janissary.gunpowder_knowledge = 8
	janissary.formation_training = 12
	janissary.formation_bonus_active = true
	
	var info = janissary.get_janissary_info()
	
	assert_eq(info["player_id"], 99, "Should export player ID")
	assert_eq(info["discipline_level"], 5, "Should export discipline level")
	assert_eq(info["scimitar_proficiency"], 15, "Should export scimitar proficiency")
	assert_eq(info["gunpowder_knowledge"], 8, "Should export gunpowder knowledge")
	assert_eq(info["formation_training"], 12, "Should export formation training")
	assert_eq(info["formation_bonus_active"], true, "Should export formation bonus status")

func test_formation_status_check():
	"""Test formation status checking utility"""
	janissary.formation_bonus_active = false
	assert_false(janissary.is_in_formation(), "Should not be in formation initially")
	
	janissary.formation_bonus_active = true
	assert_true(janissary.is_in_formation(), "Should be in formation when bonus is active")

func test_formation_bonus_signal():
	"""Test formation bonus activation signal"""
	var signal_emitted = false
	
	janissary.formation_bonus_activated.connect(func():
		signal_emitted = true
	)
	
	janissary._apply_formation_bonus(true)
	
	assert_true(signal_emitted, "Should emit formation bonus activated signal")

func test_input_handling_override():
	"""Test that Janissary properly overrides input handling for special abilities"""
	janissary.is_player_controlled = true
	janissary.can_move = true
	janissary.can_attack = true
	
	# Create mock input event for attack
	var input_event = InputEventAction.new()
	input_event.action = "attack"
	input_event.pressed = true
	
	# This would test the _unhandled_input override, but requires input simulation
	# For now, we verify the method exists and can be called
	assert_true(janissary.has_method("_unhandled_input"), "Should have input handling method")

func test_formation_range():
	"""Test formation bonus range detection"""
	assert_eq(janissary.formation_bonus_range, 64.0, "Should have 64 pixel formation range")
	
	# Test that range is used in formation detection
	# This would require multiple Janissary instances and PlayerManager mock for full test
	var range_value = janissary.formation_bonus_range
	assert_gt(range_value, 0, "Formation range should be positive")
