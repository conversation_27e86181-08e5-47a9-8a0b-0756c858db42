extends GutTest

## Test suite for CharacterAnimationSystem class
## Tests animation state management, directional animations, and sprite sheet configuration

var mock_sprite: Sprite2D
var mock_animation_player: AnimationPlayer

func before_each():
	"""Set up test environment before each test"""
	mock_sprite = Sprite2D.new()
	mock_animation_player = AnimationPlayer.new()
	
	add_child_autofree(mock_sprite)
	add_child_autofree(mock_animation_player)
	
	await wait_frames(1)

func after_each():
	"""Clean up after each test"""
	if mock_sprite and is_instance_valid(mock_sprite):
		mock_sprite.queue_free()
	if mock_animation_player and is_instance_valid(mock_animation_player):
		mock_animation_player.queue_free()

func test_animation_constants():
	"""Test that animation constants are properly defined"""
	assert_eq(CharacterAnimationSystem.ANIMATION_FRAME_SIZE, Vector2(64, 64), "Frame size should be 64x64")
	assert_eq(CharacterAnimationSystem.ANIMATION_FPS, 8, "Animation FPS should be 8")
	assert_eq(CharacterAnimationSystem.SPRITE_SHEET_COLUMNS, 8, "Should have 8 columns for directions")
	assert_eq(CharacterAnimationSystem.SPRITE_SHEET_ROWS, 8, "Should have 8 rows for animation types")

func test_animation_frame_counts():
	"""Test that animation frame counts are defined"""
	var frame_counts = CharacterAnimationSystem.ANIMATION_FRAME_COUNTS
	
	assert_true(frame_counts.has("idle"), "Should have idle frame count")
	assert_true(frame_counts.has("walk"), "Should have walk frame count")
	assert_true(frame_counts.has("run"), "Should have run frame count")
	assert_true(frame_counts.has("attack"), "Should have attack frame count")
	assert_true(frame_counts.has("scimitar_attack"), "Should have scimitar attack frame count")
	assert_true(frame_counts.has("gunpowder_shot"), "Should have gunpowder shot frame count")
	
	assert_eq(frame_counts["idle"], 4, "Idle should have 4 frames")
	assert_eq(frame_counts["walk"], 6, "Walk should have 6 frames")
	assert_eq(frame_counts["run"], 6, "Run should have 6 frames")
	assert_eq(frame_counts["attack"], 4, "Attack should have 4 frames")
	assert_eq(frame_counts["scimitar_attack"], 5, "Scimitar attack should have 5 frames")
	assert_eq(frame_counts["gunpowder_shot"], 3, "Gunpowder shot should have 3 frames")

func test_sprite_sheet_direction_mapping():
	"""Test sprite sheet direction mapping"""
	var direction_map = CharacterAnimationSystem.SPRITE_SHEET_DIRECTION_MAP
	
	assert_eq(direction_map[MovementSystem.Direction.DOWN], 0, "DOWN should map to row 0")
	assert_eq(direction_map[MovementSystem.Direction.DOWN_RIGHT], 1, "DOWN_RIGHT should map to row 1")
	assert_eq(direction_map[MovementSystem.Direction.RIGHT], 2, "RIGHT should map to row 2")
	assert_eq(direction_map[MovementSystem.Direction.UP_RIGHT], 3, "UP_RIGHT should map to row 3")
	assert_eq(direction_map[MovementSystem.Direction.UP], 4, "UP should map to row 4")
	assert_eq(direction_map[MovementSystem.Direction.UP_LEFT], 5, "UP_LEFT should map to row 5")
	assert_eq(direction_map[MovementSystem.Direction.LEFT], 6, "LEFT should map to row 6")
	assert_eq(direction_map[MovementSystem.Direction.DOWN_LEFT], 7, "DOWN_LEFT should map to row 7")

func test_sprite_sheet_setup():
	"""Test sprite sheet configuration"""
	CharacterAnimationSystem.setup_sprite_sheet(mock_sprite, "janissary")
	
	assert_eq(mock_sprite.hframes, 8, "Should set 8 horizontal frames")
	assert_eq(mock_sprite.vframes, 8, "Should set 8 vertical frames")
	assert_eq(mock_sprite.frame, 0, "Should start with frame 0")
	assert_not_null(mock_sprite.texture, "Should have a texture assigned")

func test_placeholder_texture_creation():
	"""Test placeholder texture generation"""
	var texture = CharacterAnimationSystem.create_placeholder_sprite_texture()
	
	assert_not_null(texture, "Should create texture")
	assert_true(texture is ImageTexture, "Should be ImageTexture")
	assert_eq(texture.get_width(), 512, "Should be 512 pixels wide")
	assert_eq(texture.get_height(), 512, "Should be 512 pixels tall")

func test_character_animation_setup():
	"""Test character animation setup"""
	CharacterAnimationSystem.setup_character_animations(mock_animation_player, mock_sprite, "janissary")
	
	# Check that animation library was added
	var libraries = mock_animation_player.get_animation_library_list()
	assert_true("default" in libraries, "Should have default animation library")
	
	var library = mock_animation_player.get_animation_library("default")
	assert_not_null(library, "Should have animation library")
	
	# Check that basic animations exist
	var animation_list = library.get_animation_list()
	assert_true("idle" in animation_list, "Should have idle animation")
	assert_true("walk" in animation_list, "Should have walk animation")
	assert_true("run" in animation_list, "Should have run animation")
	assert_true("attack" in animation_list, "Should have attack animation")
	assert_true("scimitar_attack" in animation_list, "Should have scimitar attack animation")

func test_idle_animation_creation():
	"""Test idle animation properties"""
	CharacterAnimationSystem.setup_character_animations(mock_animation_player, mock_sprite, "janissary")
	
	var library = mock_animation_player.get_animation_library("default")
	var idle_anim = library.get_animation("idle")
	
	assert_not_null(idle_anim, "Should have idle animation")
	assert_eq(idle_anim.length, 2.0, "Idle animation should be 2 seconds long")
	assert_eq(idle_anim.loop_mode, Animation.LOOP_LINEAR, "Idle should loop")
	assert_gt(idle_anim.get_track_count(), 0, "Should have at least one track")

func test_walk_animation_creation():
	"""Test walk animation properties"""
	CharacterAnimationSystem.setup_character_animations(mock_animation_player, mock_sprite, "janissary")
	
	var library = mock_animation_player.get_animation_library("default")
	var walk_anim = library.get_animation("walk")
	
	assert_not_null(walk_anim, "Should have walk animation")
	assert_eq(walk_anim.length, 0.75, "Walk animation should be 0.75 seconds long")
	assert_eq(walk_anim.loop_mode, Animation.LOOP_LINEAR, "Walk should loop")

func test_run_animation_creation():
	"""Test run animation properties"""
	CharacterAnimationSystem.setup_character_animations(mock_animation_player, mock_sprite, "janissary")
	
	var library = mock_animation_player.get_animation_library("default")
	var run_anim = library.get_animation("run")
	
	assert_not_null(run_anim, "Should have run animation")
	assert_eq(run_anim.length, 0.5, "Run animation should be 0.5 seconds long")
	assert_eq(run_anim.loop_mode, Animation.LOOP_LINEAR, "Run should loop")

func test_attack_animation_creation():
	"""Test attack animation properties"""
	CharacterAnimationSystem.setup_character_animations(mock_animation_player, mock_sprite, "janissary")
	
	var library = mock_animation_player.get_animation_library("default")
	var attack_anim = library.get_animation("attack")
	
	assert_not_null(attack_anim, "Should have attack animation")
	assert_eq(attack_anim.length, 0.5, "Attack animation should be 0.5 seconds long")
	assert_eq(attack_anim.loop_mode, Animation.LOOP_NONE, "Attack should not loop")

func test_scimitar_attack_animation():
	"""Test Janissary-specific scimitar attack animation"""
	CharacterAnimationSystem.setup_character_animations(mock_animation_player, mock_sprite, "janissary")
	
	var library = mock_animation_player.get_animation_library("default")
	var scimitar_anim = library.get_animation("scimitar_attack")
	
	assert_not_null(scimitar_anim, "Should have scimitar attack animation")
	assert_eq(scimitar_anim.length, 0.6, "Scimitar attack should be 0.6 seconds long")
	assert_eq(scimitar_anim.loop_mode, Animation.LOOP_NONE, "Scimitar attack should not loop")

func test_hurt_animation_creation():
	"""Test hurt animation with color effects"""
	CharacterAnimationSystem.setup_character_animations(mock_animation_player, mock_sprite, "janissary")
	
	var library = mock_animation_player.get_animation_library("default")
	var hurt_anim = library.get_animation("hurt")
	
	assert_not_null(hurt_anim, "Should have hurt animation")
	assert_eq(hurt_anim.length, 0.3, "Hurt animation should be 0.3 seconds long")
	assert_eq(hurt_anim.loop_mode, Animation.LOOP_NONE, "Hurt should not loop")
	assert_gt(hurt_anim.get_track_count(), 1, "Should have multiple tracks for color effect")

func test_death_animation_creation():
	"""Test death animation with fade effect"""
	CharacterAnimationSystem.setup_character_animations(mock_animation_player, mock_sprite, "janissary")
	
	var library = mock_animation_player.get_animation_library("default")
	var death_anim = library.get_animation("death")
	
	assert_not_null(death_anim, "Should have death animation")
	assert_eq(death_anim.length, 2.0, "Death animation should be 2 seconds long")
	assert_eq(death_anim.loop_mode, Animation.LOOP_NONE, "Death should not loop")
	assert_gt(death_anim.get_track_count(), 1, "Should have multiple tracks for fade effect")

func test_directional_animation_playback():
	"""Test directional animation playback"""
	CharacterAnimationSystem.setup_character_animations(mock_animation_player, mock_sprite, "janissary")
	
	# Test playing directional animation (should fallback to base animation)
	CharacterAnimationSystem.play_directional_animation(mock_animation_player, "walk", MovementSystem.Direction.RIGHT)
	
	# Since we don't have directional animations, it should play the base animation
	await wait_frames(1)
	assert_eq(mock_animation_player.current_animation, "walk", "Should play base walk animation")

func test_animation_state_detection():
	"""Test animation state detection from character"""
	var character = BaseCharacter.new()
	add_child_autofree(character)
	
	# Test idle state
	character.is_moving = false
	character.health = 100
	var state = CharacterAnimationSystem.get_animation_state_from_character(character)
	assert_eq(state, CharacterAnimationSystem.AnimationState.IDLE, "Should detect idle state")
	
	# Test walking state
	character.is_moving = true
	character.is_running = false
	state = CharacterAnimationSystem.get_animation_state_from_character(character)
	assert_eq(state, CharacterAnimationSystem.AnimationState.WALKING, "Should detect walking state")
	
	# Test running state
	character.is_moving = true
	character.is_running = true
	state = CharacterAnimationSystem.get_animation_state_from_character(character)
	assert_eq(state, CharacterAnimationSystem.AnimationState.RUNNING, "Should detect running state")
	
	# Test dead state
	character.health = 0
	state = CharacterAnimationSystem.get_animation_state_from_character(character)
	assert_eq(state, CharacterAnimationSystem.AnimationState.DEAD, "Should detect dead state")

func test_character_animation_update():
	"""Test character animation updating based on state"""
	var character = BaseCharacter.new()
	add_child_autofree(character)
	
	CharacterAnimationSystem.setup_character_animations(mock_animation_player, mock_sprite, "janissary")
	
	# Test idle animation
	character.is_moving = false
	character.health = 100
	character.facing_direction = Vector2.DOWN
	
	CharacterAnimationSystem.update_character_animation(character, mock_animation_player)
	await wait_frames(1)
	assert_eq(mock_animation_player.current_animation, "idle", "Should play idle animation")
	
	# Test walk animation
	character.is_moving = true
	character.is_running = false
	
	CharacterAnimationSystem.update_character_animation(character, mock_animation_player)
	await wait_frames(1)
	assert_eq(mock_animation_player.current_animation, "walk", "Should play walk animation")
	
	# Test run animation
	character.is_running = true
	
	CharacterAnimationSystem.update_character_animation(character, mock_animation_player)
	await wait_frames(1)
	assert_eq(mock_animation_player.current_animation, "run", "Should play run animation")

func test_animation_state_enum():
	"""Test animation state enum values"""
	assert_eq(CharacterAnimationSystem.AnimationState.IDLE, 0, "IDLE should be 0")
	assert_eq(CharacterAnimationSystem.AnimationState.WALKING, 1, "WALKING should be 1")
	assert_eq(CharacterAnimationSystem.AnimationState.RUNNING, 2, "RUNNING should be 2")
	assert_eq(CharacterAnimationSystem.AnimationState.ATTACKING, 3, "ATTACKING should be 3")
	assert_eq(CharacterAnimationSystem.AnimationState.CASTING, 4, "CASTING should be 4")
	assert_eq(CharacterAnimationSystem.AnimationState.HURT, 5, "HURT should be 5")
	assert_eq(CharacterAnimationSystem.AnimationState.DYING, 6, "DYING should be 6")
	assert_eq(CharacterAnimationSystem.AnimationState.DEAD, 7, "DEAD should be 7")

func test_animation_track_creation():
	"""Test that animations have proper tracks"""
	CharacterAnimationSystem.setup_character_animations(mock_animation_player, mock_sprite, "janissary")
	
	var library = mock_animation_player.get_animation_library("default")
	
	# Test idle animation tracks
	var idle_anim = library.get_animation("idle")
	assert_gt(idle_anim.get_track_count(), 0, "Idle should have tracks")
	
	# Test that first track is a value track for sprite frames
	var track_type = idle_anim.track_get_type(0)
	assert_eq(track_type, Animation.TYPE_VALUE, "First track should be value track")

func test_animation_key_insertion():
	"""Test that animations have proper keyframes"""
	CharacterAnimationSystem.setup_character_animations(mock_animation_player, mock_sprite, "janissary")
	
	var library = mock_animation_player.get_animation_library("default")
	var idle_anim = library.get_animation("idle")
	
	# Test that idle animation has keyframes
	var key_count = idle_anim.track_get_key_count(0)
	assert_gt(key_count, 0, "Idle animation should have keyframes")

func test_null_safety():
	"""Test that animation system handles null inputs safely"""
	# Test with null animation player
	CharacterAnimationSystem.setup_character_animations(null, mock_sprite, "janissary")
	# Should not crash
	
	# Test with null sprite
	CharacterAnimationSystem.setup_character_animations(mock_animation_player, null, "janissary")
	# Should not crash
	
	# Test directional animation with null player
	CharacterAnimationSystem.play_directional_animation(null, "walk", MovementSystem.Direction.DOWN)
	# Should not crash
	
	# Test character animation update with null player
	var character = BaseCharacter.new()
	add_child_autofree(character)
	CharacterAnimationSystem.update_character_animation(character, null)
	# Should not crash

func test_character_class_variations():
	"""Test that different character classes can be set up"""
	# Test Janissary setup
	CharacterAnimationSystem.setup_character_animations(mock_animation_player, mock_sprite, "janissary")
	var libraries = mock_animation_player.get_animation_library_list()
	assert_true("default" in libraries, "Should set up Janissary animations")
	
	# Clean up for next test
	mock_animation_player.clear_animation_libraries()
	
	# Test Sipahi setup (would be same for M0 prototype)
	CharacterAnimationSystem.setup_character_animations(mock_animation_player, mock_sprite, "sipahi")
	libraries = mock_animation_player.get_animation_library_list()
	assert_true("default" in libraries, "Should set up Sipahi animations")

func test_sprite_sheet_configuration():
	"""Test sprite sheet frame configuration"""
	CharacterAnimationSystem.setup_sprite_sheet(mock_sprite, "test_class")
	
	assert_eq(mock_sprite.hframes, 8, "Should configure 8 horizontal frames")
	assert_eq(mock_sprite.vframes, 8, "Should configure 8 vertical frames")
	assert_eq(mock_sprite.frame, 0, "Should start at frame 0")
	assert_not_null(mock_sprite.texture, "Should assign texture")
