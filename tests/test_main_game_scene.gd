extends GutTest

## Test suite for MainGameScene class
## Tests player spawning, camera following, multiplayer player management, and scene setup

var main_scene: MainGameScene
var mock_camera: Camera2D
var mock_ui_layer: CanvasLayer

func before_each():
	"""Set up test environment before each test"""
	# Create MainGameScene instance
	main_scene = MainGameScene.new()
	
	# Create mock camera
	mock_camera = Camera2D.new()
	mock_camera.name = "Camera2D"
	main_scene.add_child(mock_camera)
	
	# Create mock UI layer
	mock_ui_layer = CanvasLayer.new()
	mock_ui_layer.name = "UILayer"
	main_scene.add_child(mock_ui_layer)
	
	# Add to scene tree
	add_child_autofree(main_scene)
	
	# Wait for ready
	await wait_frames(1)

func after_each():
	"""Clean up after each test"""
	if main_scene and is_instance_valid(main_scene):
		main_scene.queue_free()

func test_scene_initialization():
	"""Test MainGameScene initialization"""
	assert_not_null(main_scene, "Scene should be created")
	assert_eq(main_scene.name, "MainGameScene", "Should have correct name")
	assert_not_null(main_scene.get_node("Camera2D"), "Should have camera")
	assert_not_null(main_scene.get_node("UILayer"), "Should have UI layer")

func test_camera_setup():
	"""Test camera configuration"""
	var camera = main_scene.get_node("Camera2D") as Camera2D
	
	assert_not_null(camera, "Should have camera node")
	assert_true(camera.enabled, "Camera should be enabled")
	assert_eq(camera.global_position, Vector2.ZERO, "Camera should start at origin")

func test_spawn_points_setup():
	"""Test spawn points configuration"""
	# Test that spawn points are defined
	assert_true(main_scene.spawn_points is Array, "Should have spawn points array")
	assert_gt(main_scene.spawn_points.size(), 0, "Should have at least one spawn point")
	
	# Test default spawn points
	var expected_spawns = [
		Vector2(200, 300),   # Sultanahmet spawn
		Vector2(400, 200),   # Galata spawn
		Vector2(300, 400),   # Beyoglu spawn
		Vector2(500, 350)    # Uskudar spawn
	]
	
	for i in range(min(expected_spawns.size(), main_scene.spawn_points.size())):
		assert_eq(main_scene.spawn_points[i], expected_spawns[i], "Spawn point " + str(i) + " should match expected position")

func test_player_spawning():
	"""Test player spawning functionality"""
	var player_data = PlayerManager.PlayerData.new()
	player_data.player_id = 1
	player_data.player_name = "Test Player"
	player_data.character_class = PlayerManager.CharacterClass.JANISSARY
	player_data.level = 1
	
	var spawned_player = main_scene.spawn_player(player_data)
	
	assert_not_null(spawned_player, "Should spawn player")
	assert_true(spawned_player is Player, "Should be Player instance")
	assert_eq(spawned_player.player_id, 1, "Should have correct player ID")
	assert_eq(spawned_player.character_name, "Test Player", "Should have correct name")
	assert_true(spawned_player.is_player_controlled, "Should be player controlled")
	
	# Test that player is added to scene
	assert_true(spawned_player.get_parent() == main_scene, "Player should be child of main scene")

func test_remote_player_spawning():
	"""Test remote player spawning"""
	var player_data = PlayerManager.PlayerData.new()
	player_data.player_id = 2
	player_data.player_name = "Remote Player"
	player_data.character_class = PlayerManager.CharacterClass.SIPAHI
	player_data.level = 3
	
	var remote_player = main_scene.spawn_remote_player(player_data)
	
	assert_not_null(remote_player, "Should spawn remote player")
	assert_true(remote_player is Player, "Should be Player instance")
	assert_eq(remote_player.player_id, 2, "Should have correct player ID")
	assert_eq(remote_player.character_name, "Remote Player", "Should have correct name")
	assert_false(remote_player.is_player_controlled, "Should not be player controlled")
	
	# Test that remote player is added to scene
	assert_true(remote_player.get_parent() == main_scene, "Remote player should be child of main scene")

func test_spawn_position_assignment():
	"""Test spawn position assignment for multiple players"""
	var player1_data = PlayerManager.PlayerData.new()
	player1_data.player_id = 1
	player1_data.player_name = "Player 1"
	
	var player2_data = PlayerManager.PlayerData.new()
	player2_data.player_id = 2
	player2_data.player_name = "Player 2"
	
	var player1 = main_scene.spawn_player(player1_data)
	var player2 = main_scene.spawn_remote_player(player2_data)
	
	# Players should spawn at different positions
	assert_ne(player1.global_position, player2.global_position, "Players should spawn at different positions")
	
	# Positions should be from spawn points array
	assert_true(player1.global_position in main_scene.spawn_points, "Player 1 should spawn at valid spawn point")
	assert_true(player2.global_position in main_scene.spawn_points, "Player 2 should spawn at valid spawn point")

func test_camera_following():
	"""Test camera following local player"""
	var player_data = PlayerManager.PlayerData.new()
	player_data.player_id = 1
	player_data.player_name = "Test Player"
	
	var player = main_scene.spawn_player(player_data)
	var camera = main_scene.get_node("Camera2D") as Camera2D
	
	# Set player position
	player.global_position = Vector2(500, 600)
	
	# Update camera following
	main_scene._update_camera_following()
	
	assert_eq(camera.global_position, player.global_position, "Camera should follow player position")

func test_camera_smoothing():
	"""Test camera smooth following"""
	var player_data = PlayerManager.PlayerData.new()
	player_data.player_id = 1
	player_data.player_name = "Test Player"
	
	var player = main_scene.spawn_player(player_data)
	var camera = main_scene.get_node("Camera2D") as Camera2D
	
	# Set initial positions
	camera.global_position = Vector2(100, 100)
	player.global_position = Vector2(300, 300)
	
	# Enable camera smoothing
	main_scene.camera_smoothing_enabled = true
	main_scene.camera_smoothing_speed = 5.0
	
	# Update camera with delta time
	main_scene._update_camera_smooth_following(0.1)
	
	# Camera should move towards player but not instantly
	assert_ne(camera.global_position, Vector2(100, 100), "Camera should have moved")
	assert_ne(camera.global_position, Vector2(300, 300), "Camera should not instantly reach player")
	
	var distance_to_player = camera.global_position.distance_to(player.global_position)
	var initial_distance = Vector2(100, 100).distance_to(Vector2(300, 300))
	assert_lt(distance_to_player, initial_distance, "Camera should be closer to player")

func test_player_management():
	"""Test player management functionality"""
	# Initially no players
	assert_eq(main_scene.get_player_count(), 0, "Should start with no players")
	assert_null(main_scene.get_local_player(), "Should have no local player initially")
	
	# Spawn local player
	var player_data = PlayerManager.PlayerData.new()
	player_data.player_id = 1
	player_data.player_name = "Local Player"
	
	var local_player = main_scene.spawn_player(player_data)
	
	assert_eq(main_scene.get_player_count(), 1, "Should have one player")
	assert_eq(main_scene.get_local_player(), local_player, "Should return local player")
	
	# Spawn remote player
	var remote_data = PlayerManager.PlayerData.new()
	remote_data.player_id = 2
	remote_data.player_name = "Remote Player"
	
	var remote_player = main_scene.spawn_remote_player(remote_data)
	
	assert_eq(main_scene.get_player_count(), 2, "Should have two players")
	assert_eq(main_scene.get_local_player(), local_player, "Local player should remain the same")

func test_player_removal():
	"""Test removing players from scene"""
	var player_data = PlayerManager.PlayerData.new()
	player_data.player_id = 1
	player_data.player_name = "Test Player"
	
	var player = main_scene.spawn_player(player_data)
	assert_eq(main_scene.get_player_count(), 1, "Should have one player")
	
	# Remove player
	main_scene.remove_player(1)
	
	assert_eq(main_scene.get_player_count(), 0, "Should have no players after removal")
	assert_null(main_scene.get_local_player(), "Should have no local player after removal")

func test_player_lookup():
	"""Test finding players by ID"""
	var player1_data = PlayerManager.PlayerData.new()
	player1_data.player_id = 1
	player1_data.player_name = "Player 1"
	
	var player2_data = PlayerManager.PlayerData.new()
	player2_data.player_id = 2
	player2_data.player_name = "Player 2"
	
	var player1 = main_scene.spawn_player(player1_data)
	var player2 = main_scene.spawn_remote_player(player2_data)
	
	# Test finding players by ID
	assert_eq(main_scene.get_player_by_id(1), player1, "Should find player 1")
	assert_eq(main_scene.get_player_by_id(2), player2, "Should find player 2")
	assert_null(main_scene.get_player_by_id(999), "Should return null for non-existent player")

func test_scene_bounds():
	"""Test scene boundary configuration"""
	assert_true(main_scene.scene_bounds is Rect2, "Should have scene bounds")
	assert_gt(main_scene.scene_bounds.size.x, 0, "Scene width should be positive")
	assert_gt(main_scene.scene_bounds.size.y, 0, "Scene height should be positive")
	
	# Test default Istanbul scene bounds
	var expected_bounds = Rect2(0, 0, 2048, 1536)  # 2K x 1.5K for Istanbul districts
	assert_eq(main_scene.scene_bounds, expected_bounds, "Should have Istanbul scene bounds")

func test_ui_layer_setup():
	"""Test UI layer configuration"""
	var ui_layer = main_scene.get_node("UILayer") as CanvasLayer
	
	assert_not_null(ui_layer, "Should have UI layer")
	assert_eq(ui_layer.layer, 10, "UI layer should be on layer 10")

func test_multiplayer_ready_state():
	"""Test multiplayer ready state management"""
	assert_false(main_scene.is_multiplayer_ready, "Should not be multiplayer ready initially")
	
	# Set multiplayer ready
	main_scene.set_multiplayer_ready(true)
	assert_true(main_scene.is_multiplayer_ready, "Should be multiplayer ready")
	
	# Unset multiplayer ready
	main_scene.set_multiplayer_ready(false)
	assert_false(main_scene.is_multiplayer_ready, "Should not be multiplayer ready")

func test_network_player_updates():
	"""Test network player position updates"""
	var player_data = PlayerManager.PlayerData.new()
	player_data.player_id = 2
	player_data.player_name = "Remote Player"
	
	var remote_player = main_scene.spawn_remote_player(player_data)
	var initial_position = remote_player.global_position
	
	# Update player position via network
	var new_position = Vector2(600, 700)
	var new_direction = Vector2.LEFT
	
	main_scene.update_remote_player_position(2, new_position, new_direction)
	
	assert_eq(remote_player.global_position, new_position, "Should update remote player position")
	assert_eq(remote_player.facing_direction, new_direction, "Should update remote player direction")

func test_scene_cleanup():
	"""Test scene cleanup functionality"""
	# Spawn multiple players
	var player1_data = PlayerManager.PlayerData.new()
	player1_data.player_id = 1
	player1_data.player_name = "Player 1"
	
	var player2_data = PlayerManager.PlayerData.new()
	player2_data.player_id = 2
	player2_data.player_name = "Player 2"
	
	main_scene.spawn_player(player1_data)
	main_scene.spawn_remote_player(player2_data)
	
	assert_eq(main_scene.get_player_count(), 2, "Should have two players")
	
	# Clean up scene
	main_scene.cleanup_scene()
	
	assert_eq(main_scene.get_player_count(), 0, "Should have no players after cleanup")
	assert_null(main_scene.get_local_player(), "Should have no local player after cleanup")

func test_scene_pause_resume():
	"""Test scene pause and resume functionality"""
	assert_false(main_scene.is_scene_paused, "Should not be paused initially")
	
	# Pause scene
	main_scene.pause_scene()
	assert_true(main_scene.is_scene_paused, "Should be paused")
	
	# Resume scene
	main_scene.resume_scene()
	assert_false(main_scene.is_scene_paused, "Should not be paused after resume")

func test_ottoman_theme_elements():
	"""Test Ottoman Empire themed elements in scene"""
	# Test that scene has Ottoman-themed background elements
	assert_true(main_scene.has_method("_setup_istanbul_districts"), "Should have Istanbul districts setup")
	
	# Test district names are available
	var districts = main_scene.get_district_names()
	assert_true(districts is Array, "Should have districts array")
	assert_true("Sultanahmet" in districts, "Should have Sultanahmet district")
	assert_true("Galata" in districts, "Should have Galata district")
	assert_true("Beyoglu" in districts, "Should have Beyoglu district")
	assert_true("Uskudar" in districts, "Should have Uskudar district")

func test_scene_loading_state():
	"""Test scene loading state management"""
	assert_false(main_scene.is_loading, "Should not be loading initially")
	
	# Set loading state
	main_scene.set_loading_state(true)
	assert_true(main_scene.is_loading, "Should be loading")
	
	# Clear loading state
	main_scene.set_loading_state(false)
	assert_false(main_scene.is_loading, "Should not be loading")

func test_camera_limits():
	"""Test camera boundary limits"""
	var camera = main_scene.get_node("Camera2D") as Camera2D
	
	# Test that camera limits are set based on scene bounds
	main_scene._setup_camera_limits()
	
	assert_true(camera.limit_left >= main_scene.scene_bounds.position.x, "Camera left limit should be within bounds")
	assert_true(camera.limit_right <= main_scene.scene_bounds.end.x, "Camera right limit should be within bounds")
	assert_true(camera.limit_top >= main_scene.scene_bounds.position.y, "Camera top limit should be within bounds")
	assert_true(camera.limit_bottom <= main_scene.scene_bounds.end.y, "Camera bottom limit should be within bounds")

func test_player_spawn_validation():
	"""Test player spawn validation"""
	# Test spawning with invalid data
	var invalid_data = PlayerManager.PlayerData.new()
	invalid_data.player_id = -1  # Invalid ID
	
	var invalid_player = main_scene.spawn_player(invalid_data)
	assert_null(invalid_player, "Should not spawn player with invalid data")
	
	# Test spawning duplicate player ID
	var valid_data = PlayerManager.PlayerData.new()
	valid_data.player_id = 1
	valid_data.player_name = "Valid Player"
	
	var player1 = main_scene.spawn_player(valid_data)
	assert_not_null(player1, "Should spawn first player")
	
	var duplicate_player = main_scene.spawn_player(valid_data)
	assert_null(duplicate_player, "Should not spawn duplicate player ID")

func test_scene_signals():
	"""Test that scene emits proper signals"""
	var player_spawned_signal_emitted = false
	var player_removed_signal_emitted = false
	
	main_scene.player_spawned.connect(func(player):
		player_spawned_signal_emitted = true
	)
	
	main_scene.player_removed.connect(func(player_id):
		player_removed_signal_emitted = true
	)
	
	# Spawn player
	var player_data = PlayerManager.PlayerData.new()
	player_data.player_id = 1
	player_data.player_name = "Test Player"
	
	main_scene.spawn_player(player_data)
	await wait_frames(1)
	assert_true(player_spawned_signal_emitted, "Should emit player spawned signal")
	
	# Remove player
	main_scene.remove_player(1)
	await wait_frames(1)
	assert_true(player_removed_signal_emitted, "Should emit player removed signal")
