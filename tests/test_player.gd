extends GutTest

## Test suite for Player class
## Tests player-specific functionality, input handling, and manager integration

var player: Player
var mock_sprite: Sprite2D
var mock_animation_player: AnimationPlayer
var mock_collision_shape: CollisionShape2D

func before_each():
	"""Set up test environment before each test"""
	# Create Player instance
	player = Player.new()
	
	# Create mock child nodes
	mock_sprite = Sprite2D.new()
	mock_sprite.name = "Sprite2D"
	player.add_child(mock_sprite)
	
	mock_animation_player = AnimationPlayer.new()
	mock_animation_player.name = "AnimationPlayer"
	player.add_child(mock_animation_player)
	
	mock_collision_shape = CollisionShape2D.new()
	mock_collision_shape.name = "CollisionShape2D"
	var shape = CapsuleShape2D.new()
	shape.radius = 16
	shape.height = 48
	mock_collision_shape.shape = shape
	player.add_child(mock_collision_shape)
	
	# Add to scene tree
	add_child_autofree(player)
	
	# Wait for ready
	await wait_frames(1)

func after_each():
	"""Clean up after each test"""
	if player and is_instance_valid(player):
		player.queue_free()

func test_player_initialization():
	"""Test Player class initialization"""
	assert_eq(player.character_name, "Ottoman Player", "Should have player-specific name")
	assert_true(player.is_player_controlled, "Should be player controlled by default")
	assert_eq(player.player_id, -1, "Should have default player ID")
	assert_eq(player.experience, 0, "Should start with 0 experience")
	assert_eq(player.akce_coins, 0, "Should start with 0 akçe coins")
	assert_true(player.can_attack, "Should be able to attack initially")
	assert_eq(player.attack_cooldown, 0.0, "Should have no attack cooldown initially")

func test_player_data_initialization():
	"""Test player initialization with PlayerData"""
	var player_data = PlayerManager.PlayerData.new()
	player_data.player_id = 123
	player_data.player_name = "Test Warrior"
	player_data.character_class = PlayerManager.CharacterClass.JANISSARY
	player_data.level = 5
	player_data.health = 150
	player_data.max_health = 150
	player_data.stamina = 120
	player_data.max_stamina = 120
	player_data.attack_power = 25
	player_data.defense = 15
	player_data.current_stance = "disciplined"
	player_data.akce_coins = 100
	
	player.initialize_player(player_data)
	
	assert_eq(player.player_id, 123, "Should set player ID")
	assert_eq(player.character_name, "Test Warrior", "Should set player name")
	assert_eq(player.character_class, PlayerManager.CharacterClass.JANISSARY, "Should set character class")
	assert_eq(player.level, 5, "Should set level")
	assert_eq(player.health, 150, "Should set health")
	assert_eq(player.max_health, 150, "Should set max health")
	assert_eq(player.stamina, 120, "Should set stamina")
	assert_eq(player.max_stamina, 120, "Should set max stamina")
	assert_eq(player.attack_power, 25, "Should set attack power")
	assert_eq(player.defense, 15, "Should set defense")
	assert_eq(player.current_stance, "disciplined", "Should set stance")
	assert_eq(player.akce_coins, 100, "Should set akçe coins")

func test_class_specific_properties():
	"""Test that different character classes get appropriate properties"""
	var janissary_data = PlayerManager.PlayerData.new()
	janissary_data.character_class = PlayerManager.CharacterClass.JANISSARY
	player.initialize_player(janissary_data)
	player._apply_class_properties()
	
	assert_eq(player.base_speed, 120.0, "Janissary should have balanced speed")
	assert_eq(player.attack_cooldown_time, 1.0, "Janissary should have standard attack speed")
	assert_eq(player.current_stance, "disciplined", "Janissary should default to disciplined stance")
	
	# Test Sipahi properties
	var sipahi_data = PlayerManager.PlayerData.new()
	sipahi_data.character_class = PlayerManager.CharacterClass.SIPAHI
	player.initialize_player(sipahi_data)
	player._apply_class_properties()
	
	assert_eq(player.base_speed, 140.0, "Sipahi should be faster")
	assert_eq(player.attack_cooldown_time, 0.8, "Sipahi should attack faster")
	assert_eq(player.current_stance, "charge", "Sipahi should default to charge stance")
	
	# Test Sufi Mystic properties
	var sufi_data = PlayerManager.PlayerData.new()
	sufi_data.character_class = PlayerManager.CharacterClass.SUFI_MYSTIC
	player.initialize_player(sufi_data)
	player._apply_class_properties()
	
	assert_eq(player.base_speed, 110.0, "Sufi should be slower but mystical")
	assert_eq(player.attack_cooldown_time, 1.2, "Sufi should have slower attacks")
	assert_eq(player.current_stance, "whirling", "Sufi should default to whirling stance")

func test_attack_system():
	"""Test player attack functionality"""
	player.player_id = 1
	player.can_attack = true
	player.attack_cooldown = 0.0
	player.facing_direction = Vector2.RIGHT
	player.global_position = Vector2(100, 100)
	
	# Test attack execution
	player._perform_attack()
	
	assert_false(player.can_attack, "Should not be able to attack immediately after attacking")
	assert_gt(player.attack_cooldown, 0.0, "Should have attack cooldown after attacking")

func test_attack_cooldown_recovery():
	"""Test that attack cooldown recovers over time"""
	player.can_attack = false
	player.attack_cooldown = 1.0
	
	# Simulate time passing
	player._process(0.5)  # Half second
	assert_eq(player.attack_cooldown, 0.5, "Cooldown should decrease")
	assert_false(player.can_attack, "Should still not be able to attack")
	
	player._process(0.6)  # Another 0.6 seconds (total 1.1)
	assert_le(player.attack_cooldown, 0.0, "Cooldown should be finished")
	assert_true(player.can_attack, "Should be able to attack again")

func test_stance_changing():
	"""Test player stance changing functionality"""
	var stance_changed_signal_emitted = false
	var emitted_stance = ""
	
	player.stance_changed.connect(func(new_stance):
		stance_changed_signal_emitted = true
		emitted_stance = new_stance
	)
	
	player._change_stance("charge")
	
	assert_eq(player.current_stance, "charge", "Should change to charge stance")
	assert_true(stance_changed_signal_emitted, "Should emit stance changed signal")
	assert_eq(emitted_stance, "charge", "Should emit correct stance")
	
	# Test changing to same stance (should not change)
	stance_changed_signal_emitted = false
	player._change_stance("charge")
	assert_false(stance_changed_signal_emitted, "Should not emit signal when changing to same stance")

func test_experience_system():
	"""Test experience gaining functionality"""
	player.player_id = 1
	var initial_experience = player.experience
	
	player.add_experience(50)
	
	assert_eq(player.experience, initial_experience + 50, "Should gain experience")

func test_akce_system():
	"""Test akçe coin management"""
	player.player_id = 1
	var initial_akce = player.akce_coins
	
	# Test adding akçe
	player.add_akce(100)
	assert_eq(player.akce_coins, initial_akce + 100, "Should gain akçe coins")
	
	# Test spending akçe (mock PlayerManager response)
	player.akce_coins = 50
	# Note: This would need PlayerManager mock to fully test spending

func test_combat_action_signal():
	"""Test that combat actions emit proper signals"""
	var signal_emitted = false
	var emitted_action = ""
	
	player.combat_action_performed.connect(func(action_type):
		signal_emitted = true
		emitted_action = action_type
	)
	
	player.can_attack = true
	player._perform_attack()
	
	await wait_frames(1)
	
	assert_true(signal_emitted, "Should emit combat action signal")
	assert_eq(emitted_action, "light_attack", "Should emit correct action type")

func test_player_info_export():
	"""Test comprehensive player information export"""
	player.player_id = 42
	player.character_name = "Test Player"
	player.level = 10
	player.experience = 500
	player.akce_coins = 250
	player.health = 80
	player.global_position = Vector2(200, 300)
	player.facing_direction = Vector2.LEFT
	player.is_moving = true
	player.current_stance = "whirling"
	
	var info = player.get_player_info()
	
	assert_eq(info["player_id"], 42, "Should export player ID")
	assert_eq(info["name"], "Test Player", "Should export name")
	assert_eq(info["level"], 10, "Should export level")
	assert_eq(info["experience"], 500, "Should export experience")
	assert_eq(info["akce_coins"], 250, "Should export akçe coins")
	assert_eq(info["health"], 80, "Should export health")
	assert_eq(info["position"], Vector2(200, 300), "Should export position")
	assert_eq(info["facing_direction"], Vector2.LEFT, "Should export facing direction")
	assert_eq(info["is_moving"], true, "Should export movement state")
	assert_eq(info["stance"], "whirling", "Should export stance")

func test_respawn_functionality():
	"""Test player respawn system"""
	# Kill the player first
	player.take_damage(200)
	assert_eq(player.health, 0, "Player should be dead")
	assert_false(player.can_move, "Player should not be able to move")
	
	# Respawn at new position
	var respawn_pos = Vector2(500, 600)
	player.respawn(respawn_pos)
	
	assert_eq(player.global_position, respawn_pos, "Should respawn at correct position")
	assert_eq(player.health, player.max_health, "Should have full health after respawn")
	assert_eq(player.stamina, player.max_stamina, "Should have full stamina after respawn")
	assert_true(player.can_move, "Should be able to move after respawn")

func test_input_handling_setup():
	"""Test that player input handling is properly configured"""
	assert_true(player.is_player_controlled, "Should be player controlled")
	
	# Test disabling player control
	player.set_player_controlled(false)
	assert_false(player.is_player_controlled, "Should not be player controlled after disabling")
	
	# Test re-enabling player control
	player.set_player_controlled(true)
	assert_true(player.is_player_controlled, "Should be player controlled after re-enabling")

func test_equipment_system_foundation():
	"""Test basic equipment system structure"""
	assert_true(player.equipped_weapon is Dictionary, "Should have equipped weapon dictionary")
	assert_true(player.inventory is Array, "Should have inventory array")

func test_manager_integration_signals():
	"""Test that player properly handles manager signals"""
	# This would require mocking the managers, but we can test the signal connection structure
	assert_true(player.has_signal("combat_action_performed"), "Should have combat action signal")
	assert_true(player.has_signal("stance_changed"), "Should have stance changed signal")

func test_network_position_updates():
	"""Test position updates for network synchronization"""
	player.player_id = 1
	player.is_player_controlled = true
	player.global_position = Vector2(100, 200)
	player.facing_direction = Vector2.UP
	player.is_moving = true
	player.is_running = false
	
	# This would normally update PlayerManager, but we can test the call structure
	# The actual network update would require PlayerManager mock
	
	# Test that the player maintains its state correctly
	assert_eq(player.global_position, Vector2(100, 200), "Should maintain position")
	assert_eq(player.facing_direction, Vector2.UP, "Should maintain facing direction")
	assert_true(player.is_moving, "Should maintain movement state")
	assert_false(player.is_running, "Should maintain running state")
