extends RefCounted

## Test Configuration for Moon-Break Ottoman Empire MMORPG
## Provides mock implementations and test utilities

class_name TestConfig

# Mock implementations for testing without full game systems
class MockPlayerManager:
	enum CharacterClass {
		JANISSARY = 0,
		SIPAHI = 1,
		SUFI_MYSTIC = 2
	}
	
	class PlayerData:
		var player_id: int = -1
		var player_name: String = ""
		var character_class: CharacterClass = CharacterClass.JANISSARY
		var level: int = 1
		var health: int = 100
		var max_health: int = 100
		var stamina: int = 100
		var max_stamina: int = 100
		var attack_power: int = 10
		var defense: int = 5
		var current_stance: String = "disciplined"
		var experience: int = 0
		var akce_coins: int = 0

class MockGameManager:
	static var instance: MockGameManager
	var is_multiplayer: bool = false
	var max_players: int = 4
	
	static func get_instance() -> MockGameManager:
		if not instance:
			instance = MockGameManager.new()
		return instance

class MockNetworkManager:
	static var instance: MockNetworkManager
	var is_server: bool = false
	var is_client: bool = false
	var connected_players: Array = []
	
	static func get_instance() -> MockNetworkManager:
		if not instance:
			instance = MockNetworkManager.new()
		return instance
	
	func send_player_update(player_data: Dictionary):
		pass
	
	func send_combat_action(action_data: Dictionary):
		pass

# Test utilities
class TestUtils:
	static func create_mock_character_body() -> CharacterBody2D:
		var character = CharacterBody2D.new()
		character.collision_layer = 1
		character.collision_mask = 4
		
		# Add collision shape
		var collision = CollisionShape2D.new()
		var shape = CapsuleShape2D.new()
		shape.radius = 16
		shape.height = 48
		collision.shape = shape
		character.add_child(collision)
		
		return character
	
	static func create_mock_sprite() -> Sprite2D:
		var sprite = Sprite2D.new()
		sprite.texture = create_test_texture()
		return sprite
	
	static func create_mock_animation_player() -> AnimationPlayer:
		var player = AnimationPlayer.new()
		return player
	
	static func create_test_texture() -> ImageTexture:
		var image = Image.create(64, 64, false, Image.FORMAT_RGBA8)
		image.fill(Color.WHITE)
		var texture = ImageTexture.new()
		texture.set_image(image)
		return texture
	
	static func wait_frames(frames: int):
		for i in range(frames):
			await Engine.get_main_loop().process_frame

# Mock base test class (simplified GUT replacement)
class GutTest extends RefCounted:
	var _auto_free_nodes: Array = []
	
	func add_child_autofree(node: Node) -> Node:
		_auto_free_nodes.append(node)
		Engine.get_main_loop().current_scene.add_child(node)
		return node
	
	func _cleanup():
		for node in _auto_free_nodes:
			if is_instance_valid(node):
				node.queue_free()
		_auto_free_nodes.clear()
	
	# Assertion methods
	func assert_eq(actual, expected, message: String = ""):
		if actual != expected:
			var error_msg = "Expected " + str(expected) + " but got " + str(actual)
			if message != "":
				error_msg = message + ": " + error_msg
			push_error(error_msg)
			assert(false, error_msg)
	
	func assert_ne(actual, not_expected, message: String = ""):
		if actual == not_expected:
			var error_msg = "Expected not " + str(not_expected) + " but got " + str(actual)
			if message != "":
				error_msg = message + ": " + error_msg
			push_error(error_msg)
			assert(false, error_msg)
	
	func assert_true(value, message: String = ""):
		if not value:
			var error_msg = "Expected true but got " + str(value)
			if message != "":
				error_msg = message + ": " + error_msg
			push_error(error_msg)
			assert(false, error_msg)
	
	func assert_false(value, message: String = ""):
		if value:
			var error_msg = "Expected false but got " + str(value)
			if message != "":
				error_msg = message + ": " + error_msg
			push_error(error_msg)
			assert(false, error_msg)
	
	func assert_not_null(value, message: String = ""):
		if value == null:
			var error_msg = "Expected non-null value"
			if message != "":
				error_msg = message + ": " + error_msg
			push_error(error_msg)
			assert(false, error_msg)
	
	func assert_null(value, message: String = ""):
		if value != null:
			var error_msg = "Expected null but got " + str(value)
			if message != "":
				error_msg = message + ": " + error_msg
			push_error(error_msg)
			assert(false, error_msg)
	
	func assert_almost_eq(actual: float, expected: float, tolerance: float, message: String = ""):
		if abs(actual - expected) > tolerance:
			var error_msg = "Expected " + str(expected) + " ± " + str(tolerance) + " but got " + str(actual)
			if message != "":
				error_msg = message + ": " + error_msg
			push_error(error_msg)
			assert(false, error_msg)
	
	func assert_gt(actual, expected, message: String = ""):
		if not (actual > expected):
			var error_msg = "Expected " + str(actual) + " > " + str(expected)
			if message != "":
				error_msg = message + ": " + error_msg
			push_error(error_msg)
			assert(false, error_msg)
	
	func assert_lt(actual, expected, message: String = ""):
		if not (actual < expected):
			var error_msg = "Expected " + str(actual) + " < " + str(expected)
			if message != "":
				error_msg = message + ": " + error_msg
			push_error(error_msg)
			assert(false, error_msg)
	
	func assert_ge(actual, expected, message: String = ""):
		if not (actual >= expected):
			var error_msg = "Expected " + str(actual) + " >= " + str(expected)
			if message != "":
				error_msg = message + ": " + error_msg
			push_error(error_msg)
			assert(false, error_msg)
	
	func assert_le(actual, expected, message: String = ""):
		if not (actual <= expected):
			var error_msg = "Expected " + str(actual) + " <= " + str(expected)
			if message != "":
				error_msg = message + ": " + error_msg
			push_error(error_msg)
			assert(false, error_msg)
	
	func wait_frames(frames: int):
		return TestUtils.wait_frames(frames)

# Global test setup
static func setup_test_environment():
	"""Set up global test environment"""
	# Mock singletons
	if not Engine.has_singleton("PlayerManager"):
		Engine.register_singleton("PlayerManager", MockPlayerManager.new())
	
	if not Engine.has_singleton("GameManager"):
		Engine.register_singleton("GameManager", MockGameManager.new())
	
	if not Engine.has_singleton("NetworkManager"):
		Engine.register_singleton("NetworkManager", MockNetworkManager.new())

static func cleanup_test_environment():
	"""Clean up test environment"""
	# Clean up would happen here if needed
	pass
