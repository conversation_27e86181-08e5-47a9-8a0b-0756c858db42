extends SceneTree

## Test Runner for Moon-Break: Chronicles of the Jade Empire
## Runs all unit tests for the Ottoman Empire MMORPG character system

# Test configuration
const TEST_TIMEOUT = 30.0  # 30 seconds per test suite
const VERBOSE_OUTPUT = true

# Test results tracking
var total_tests = 0
var passed_tests = 0
var failed_tests = 0
var test_results = {}

func _init():
	"""Initialize test runner"""
	print("=== Moon-Break Ottoman Empire MMORPG Test Suite ===")
	print("Testing Character System Implementation")
	print("=" * 50)
	
	# Run all test suites
	await run_all_tests()
	
	# Print final results
	print_final_results()
	
	# Exit with appropriate code
	var exit_code = 0 if failed_tests == 0 else 1
	quit(exit_code)

func run_all_tests():
	"""Run all test suites"""
	var test_suites = [
		{
			"name": "BaseCharacter Tests",
			"script": "res://tests/test_base_character.gd",
			"description": "Tests core character functionality, movement, combat, and health systems"
		},
		{
			"name": "Player Tests", 
			"script": "res://tests/test_player.gd",
			"description": "Tests player-specific functionality, input handling, and manager integration"
		},
		{
			"name": "Janissary Tests",
			"script": "res://tests/test_janissary.gd", 
			"description": "Tests Ottoman elite infantry features, formation bonuses, and combat abilities"
		},
		{
			"name": "Movement System Tests",
			"script": "res://tests/test_movement_system.gd",
			"description": "Tests 8-directional movement, stance modifiers, and stamina calculations"
		},
		{
			"name": "Animation System Tests",
			"script": "res://tests/test_animation_system.gd",
			"description": "Tests animation state management, directional animations, and sprite sheets"
		},
		{
			"name": "Main Game Scene Tests",
			"script": "res://tests/test_main_game_scene.gd",
			"description": "Tests player spawning, camera following, and multiplayer scene management"
		}
	]
	
	for suite in test_suites:
		await run_test_suite(suite)

func run_test_suite(suite_info: Dictionary):
	"""Run a single test suite"""
	print("\n" + "=" * 60)
	print("Running: " + suite_info.name)
	print("Description: " + suite_info.description)
	print("Script: " + suite_info.script)
	print("-" * 60)
	
	var start_time = Time.get_time_dict_from_system()
	
	# Check if test file exists
	if not FileAccess.file_exists(suite_info.script):
		print("❌ ERROR: Test file not found: " + suite_info.script)
		test_results[suite_info.name] = {
			"status": "ERROR",
			"message": "Test file not found",
			"tests": 0,
			"passed": 0,
			"failed": 1
		}
		failed_tests += 1
		return
	
	# Load and run test script
	var test_script = load(suite_info.script)
	if not test_script:
		print("❌ ERROR: Could not load test script: " + suite_info.script)
		test_results[suite_info.name] = {
			"status": "ERROR", 
			"message": "Could not load test script",
			"tests": 0,
			"passed": 0,
			"failed": 1
		}
		failed_tests += 1
		return
	
	# Create test instance
	var test_instance = test_script.new()
	if not test_instance:
		print("❌ ERROR: Could not create test instance")
		test_results[suite_info.name] = {
			"status": "ERROR",
			"message": "Could not create test instance", 
			"tests": 0,
			"passed": 0,
			"failed": 1
		}
		failed_tests += 1
		return
	
	# Run tests (simplified - in real implementation would use GUT framework)
	var suite_results = await run_test_methods(test_instance, suite_info.name)
	
	var end_time = Time.get_time_dict_from_system()
	var duration = calculate_duration(start_time, end_time)
	
	# Store results
	test_results[suite_info.name] = suite_results
	test_results[suite_info.name]["duration"] = duration
	
	# Update totals
	total_tests += suite_results.tests
	passed_tests += suite_results.passed
	failed_tests += suite_results.failed
	
	# Print suite results
	print_suite_results(suite_info.name, suite_results, duration)

func run_test_methods(test_instance, suite_name: String) -> Dictionary:
	"""Run all test methods in a test instance"""
	var results = {
		"status": "PASSED",
		"message": "",
		"tests": 0,
		"passed": 0,
		"failed": 0,
		"methods": {}
	}
	
	# Get all methods that start with "test_"
	var methods = []
	for method in test_instance.get_method_list():
		if method.name.begins_with("test_"):
			methods.append(method.name)
	
	results.tests = methods.size()
	
	if methods.is_empty():
		print("⚠️  WARNING: No test methods found in " + suite_name)
		results.status = "WARNING"
		results.message = "No test methods found"
		return results
	
	print("Found " + str(methods.size()) + " test methods")
	
	# Run each test method
	for method_name in methods:
		var method_result = await run_single_test_method(test_instance, method_name)
		results.methods[method_name] = method_result
		
		if method_result.passed:
			results.passed += 1
			if VERBOSE_OUTPUT:
				print("  ✅ " + method_name + " - PASSED")
		else:
			results.failed += 1
			results.status = "FAILED"
			print("  ❌ " + method_name + " - FAILED: " + method_result.error)
	
	return results

func run_single_test_method(test_instance, method_name: String) -> Dictionary:
	"""Run a single test method"""
	var result = {
		"passed": false,
		"error": "",
		"duration": 0.0
	}
	
	var start_time = Time.get_time_dict_from_system()
	
	try:
		# Setup
		if test_instance.has_method("before_each"):
			await test_instance.before_each()
		
		# Run test
		await test_instance.call(method_name)
		
		# Teardown
		if test_instance.has_method("after_each"):
			await test_instance.after_each()
		
		result.passed = true
		
	except Exception as e:
		result.error = str(e)
		result.passed = false
	
	var end_time = Time.get_time_dict_from_system()
	result.duration = calculate_duration(start_time, end_time)
	
	return result

func print_suite_results(suite_name: String, results: Dictionary, duration: float):
	"""Print results for a test suite"""
	var status_icon = "✅" if results.status == "PASSED" else "❌"
	var status_color = "[color=green]" if results.status == "PASSED" else "[color=red]"
	
	print("\n" + status_icon + " " + suite_name + " Results:")
	print("  Status: " + results.status)
	print("  Tests: " + str(results.tests))
	print("  Passed: " + str(results.passed))
	print("  Failed: " + str(results.failed))
	print("  Duration: " + str(duration) + "s")
	
	if results.failed > 0:
		print("  Failed Methods:")
		for method_name in results.methods:
			var method_result = results.methods[method_name]
			if not method_result.passed:
				print("    - " + method_name + ": " + method_result.error)

func print_final_results():
	"""Print final test results summary"""
	print("\n" + "=" * 60)
	print("FINAL TEST RESULTS")
	print("=" * 60)
	
	var overall_status = "PASSED" if failed_tests == 0 else "FAILED"
	var status_icon = "✅" if failed_tests == 0 else "❌"
	
	print(status_icon + " Overall Status: " + overall_status)
	print("📊 Total Tests: " + str(total_tests))
	print("✅ Passed: " + str(passed_tests))
	print("❌ Failed: " + str(failed_tests))
	
	if total_tests > 0:
		var pass_rate = (float(passed_tests) / float(total_tests)) * 100.0
		print("📈 Pass Rate: " + str(pass_rate).pad_decimals(1) + "%")
	
	print("\n" + "=" * 60)
	print("SUITE BREAKDOWN")
	print("=" * 60)
	
	for suite_name in test_results:
		var suite_result = test_results[suite_name]
		var suite_icon = "✅" if suite_result.status == "PASSED" else "❌"
		print(suite_icon + " " + suite_name + ": " + str(suite_result.passed) + "/" + str(suite_result.tests) + " passed")
	
	if failed_tests == 0:
		print("\n🎉 All tests passed! The Ottoman Empire character system is ready for battle!")
		print("🏰 The Janissaries stand ready to defend Constantinople!")
	else:
		print("\n⚔️  Some tests failed. The Ottoman forces need reinforcement!")
		print("🔧 Review the failed tests and strengthen the empire's defenses.")
	
	print("\n" + "=" * 60)

func calculate_duration(start_time: Dictionary, end_time: Dictionary) -> float:
	"""Calculate duration between two time dictionaries"""
	var start_seconds = start_time.hour * 3600 + start_time.minute * 60 + start_time.second
	var end_seconds = end_time.hour * 3600 + end_time.minute * 60 + end_time.second
	return float(end_seconds - start_seconds)

# Mock assertion functions for basic testing without GUT
class TestAssertions:
	static func assert_eq(actual, expected, message: String = ""):
		if actual != expected:
			var error_msg = "Assertion failed: expected " + str(expected) + " but got " + str(actual)
			if message != "":
				error_msg += " - " + message
			push_error(error_msg)
			assert(false, error_msg)
	
	static func assert_ne(actual, not_expected, message: String = ""):
		if actual == not_expected:
			var error_msg = "Assertion failed: expected not " + str(not_expected) + " but got " + str(actual)
			if message != "":
				error_msg += " - " + message
			push_error(error_msg)
			assert(false, error_msg)
	
	static func assert_true(value, message: String = ""):
		if not value:
			var error_msg = "Assertion failed: expected true but got " + str(value)
			if message != "":
				error_msg += " - " + message
			push_error(error_msg)
			assert(false, error_msg)
	
	static func assert_false(value, message: String = ""):
		if value:
			var error_msg = "Assertion failed: expected false but got " + str(value)
			if message != "":
				error_msg += " - " + message
			push_error(error_msg)
			assert(false, error_msg)
	
	static func assert_not_null(value, message: String = ""):
		if value == null:
			var error_msg = "Assertion failed: expected non-null value"
			if message != "":
				error_msg += " - " + message
			push_error(error_msg)
			assert(false, error_msg)
	
	static func assert_null(value, message: String = ""):
		if value != null:
			var error_msg = "Assertion failed: expected null but got " + str(value)
			if message != "":
				error_msg += " - " + message
			push_error(error_msg)
			assert(false, error_msg)
	
	static func assert_almost_eq(actual: float, expected: float, tolerance: float, message: String = ""):
		if abs(actual - expected) > tolerance:
			var error_msg = "Assertion failed: expected " + str(expected) + " ± " + str(tolerance) + " but got " + str(actual)
			if message != "":
				error_msg += " - " + message
			push_error(error_msg)
			assert(false, error_msg)
	
	static func assert_gt(actual, expected, message: String = ""):
		if not (actual > expected):
			var error_msg = "Assertion failed: expected " + str(actual) + " > " + str(expected)
			if message != "":
				error_msg += " - " + message
			push_error(error_msg)
			assert(false, error_msg)
	
	static func assert_lt(actual, expected, message: String = ""):
		if not (actual < expected):
			var error_msg = "Assertion failed: expected " + str(actual) + " < " + str(expected)
			if message != "":
				error_msg += " - " + message
			push_error(error_msg)
			assert(false, error_msg)
	
	static func assert_ge(actual, expected, message: String = ""):
		if not (actual >= expected):
			var error_msg = "Assertion failed: expected " + str(actual) + " >= " + str(expected)
			if message != "":
				error_msg += " - " + message
			push_error(error_msg)
			assert(false, error_msg)
	
	static func assert_le(actual, expected, message: String = ""):
		if not (actual <= expected):
			var error_msg = "Assertion failed: expected " + str(actual) + " <= " + str(expected)
			if message != "":
				error_msg += " - " + message
			push_error(error_msg)
			assert(false, error_msg)
