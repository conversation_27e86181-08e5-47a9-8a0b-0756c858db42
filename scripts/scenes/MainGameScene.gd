extends Node2D
class_name MainGameScene

## MainGameScene - Main gameplay scene for Ottoman Empire MMORPG
## Handles player spawning, camera management, and basic world setup

signal player_spawned(player: Player)
signal scene_ready()

# Scene references
@onready var camera: Camera2D = $Camera2D
@onready var player_spawn_point: Marker2D = $PlayerSpawnPoint
@onready var world_container: Node2D = $WorldContainer
@onready var ui_layer: CanvasLayer = $UILayer
@onready var players_container: Node2D = $PlayersContainer

# Player management
var local_player: Player = null
var remote_players: Dictionary = {}  # player_id -> Player instance

# World properties
var current_district: String = "sultanahmet"
var world_bounds: Rect2 = Rect2(-1000, -1000, 2000, 2000)

# Camera properties
var camera_follow_speed: float = 5.0
var camera_zoom_level: float = 2.0
var camera_bounds_enabled: bool = true

func _ready():
	print("MainGameScene initializing...")
	
	# Set up camera
	_setup_camera()
	
	# Set up world
	_setup_world()
	
	# Connect to managers
	_connect_to_managers()
	
	# Spawn local player
	_spawn_local_player()
	
	print("MainGameScene ready!")
	scene_ready.emit()

func _setup_camera():
	"""Set up the main camera"""
	if camera:
		camera.zoom = Vector2(camera_zoom_level, camera_zoom_level)
		camera.position_smoothing_enabled = true
		camera.position_smoothing_speed = camera_follow_speed
		
		# Set camera limits if bounds are enabled
		if camera_bounds_enabled:
			camera.limit_left = int(world_bounds.position.x)
			camera.limit_top = int(world_bounds.position.y)
			camera.limit_right = int(world_bounds.position.x + world_bounds.size.x)
			camera.limit_bottom = int(world_bounds.position.y + world_bounds.size.y)
		
		print("Camera configured with zoom: ", camera_zoom_level)

func _setup_world():
	"""Set up the basic world environment"""
	# Create a simple ground texture for testing
	_create_test_ground()
	
	# Add some basic world elements
	_create_test_environment()
	
	print("World setup complete for district: ", current_district)

func _create_test_ground():
	"""Create a simple ground for testing"""
	var ground = Sprite2D.new()
	ground.name = "Ground"
	
	# Create a simple ground texture
	var image = Image.create(2000, 2000, false, Image.FORMAT_RGBA8)
	image.fill(Color(0.4, 0.3, 0.2))  # Brown ground color
	
	# Add some texture pattern
	for y in range(0, 2000, 64):
		for x in range(0, 2000, 64):
			# Add cobblestone pattern
			var stone_color = Color(0.5, 0.4, 0.3) if (x/64 + y/64) % 2 == 0 else Color(0.45, 0.35, 0.25)
			for sy in range(64):
				for sx in range(64):
					if x + sx < 2000 and y + sy < 2000:
						image.set_pixel(x + sx, y + sy, stone_color)
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	ground.texture = texture
	ground.position = Vector2.ZERO
	ground.z_index = -10  # Behind everything
	
	world_container.add_child(ground)

func _create_test_environment():
	"""Create some basic environmental elements for testing"""
	# Add some test obstacles/buildings
	for i in range(5):
		var building = _create_test_building(Vector2(randf_range(-400, 400), randf_range(-400, 400)))
		world_container.add_child(building)
	
	# Add spawn point marker
	if player_spawn_point:
		player_spawn_point.position = Vector2.ZERO

func _create_test_building(pos: Vector2) -> StaticBody2D:
	"""Create a test building/obstacle"""
	var building = StaticBody2D.new()
	building.name = "TestBuilding"
	building.position = pos
	
	# Visual representation
	var sprite = Sprite2D.new()
	var image = Image.create(128, 96, false, Image.FORMAT_RGBA8)
	image.fill(Color(0.6, 0.5, 0.4))  # Ottoman building color
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	sprite.texture = texture
	building.add_child(sprite)
	
	# Collision shape
	var collision = CollisionShape2D.new()
	var shape = RectangleShape2D.new()
	shape.size = Vector2(128, 96)
	collision.shape = shape
	building.add_child(collision)
	
	# Set collision layers (Environment = layer 3)
	building.collision_layer = 4
	building.collision_mask = 0
	
	return building

func _connect_to_managers():
	"""Connect to game managers"""
	# Connect to NetworkManager for multiplayer
	if NetworkManager:
		NetworkManager.player_connected.connect(_on_player_connected)
		NetworkManager.player_disconnected.connect(_on_player_disconnected)
		NetworkManager.player_position_updated.connect(_on_player_position_updated)
	
	# Connect to PlayerManager
	if PlayerManager:
		PlayerManager.player_stats_changed.connect(_on_player_stats_changed)

func _spawn_local_player():
	"""Spawn the local player character"""
	if local_player:
		print("Local player already exists!")
		return
	
	# Get player data from PlayerManager
	var player_data = PlayerManager.get_local_player_data()
	if not player_data:
		print("No local player data found, creating default Janissary")
		player_data = PlayerManager.create_default_player_data("Local Player", PlayerManager.CharacterClass.JANISSARY)
	
	# Create appropriate character class
	match player_data.character_class:
		PlayerManager.CharacterClass.JANISSARY:
			local_player = preload("res://scripts/characters/Janissary.gd").new()
		PlayerManager.CharacterClass.SIPAHI:
			local_player = Player.new()  # Will implement Sipahi later
		PlayerManager.CharacterClass.SUFI_MYSTIC:
			local_player = Player.new()  # Will implement Sufi later
		_:
			local_player = Player.new()
	
	# Set up player scene structure
	_setup_player_scene_structure(local_player)
	
	# Initialize player with data
	local_player.initialize_player(player_data)
	
	# Set spawn position
	var spawn_pos = player_spawn_point.position if player_spawn_point else Vector2.ZERO
	local_player.global_position = spawn_pos
	
	# Add to scene
	players_container.add_child(local_player)
	
	# Set camera to follow player
	if camera:
		camera.global_position = local_player.global_position
	
	print("Local player spawned: ", local_player.character_name, " at ", spawn_pos)
	player_spawned.emit(local_player)

func _setup_player_scene_structure(player: Player):
	"""Set up the scene structure for a player character"""
	# Add Sprite2D
	var sprite = Sprite2D.new()
	sprite.name = "Sprite2D"
	player.add_child(sprite)
	
	# Set up sprite with animation system
	CharacterAnimationSystem.setup_sprite_sheet(sprite, "janissary")
	
	# Add AnimationPlayer
	var animation_player = AnimationPlayer.new()
	animation_player.name = "AnimationPlayer"
	player.add_child(animation_player)
	
	# Set up animations
	CharacterAnimationSystem.setup_character_animations(animation_player, sprite, "janissary")
	
	# Add CollisionShape2D
	var collision_shape = CollisionShape2D.new()
	collision_shape.name = "CollisionShape2D"
	var shape = CapsuleShape2D.new()
	shape.radius = 16
	shape.height = 48
	collision_shape.shape = shape
	player.add_child(collision_shape)
	
	print("Player scene structure set up")

func _physics_process(delta):
	"""Handle per-frame updates"""
	# Update camera to follow local player
	if local_player and camera:
		camera.global_position = local_player.global_position

# Network event handlers
func _on_player_connected(player_id: int):
	"""Handle remote player connection"""
	print("Remote player connected: ", player_id)
	
	# Spawn remote player (simplified for M0)
	var remote_player = Player.new()
	remote_player.player_id = player_id
	remote_player.character_name = "Remote Player " + str(player_id)
	remote_player.is_player_controlled = false
	
	_setup_player_scene_structure(remote_player)
	
	# Add to scene
	players_container.add_child(remote_player)
	remote_players[player_id] = remote_player
	
	print("Remote player spawned: ", player_id)

func _on_player_disconnected(player_id: int):
	"""Handle remote player disconnection"""
	print("Remote player disconnected: ", player_id)
	
	if player_id in remote_players:
		var remote_player = remote_players[player_id]
		remote_player.queue_free()
		remote_players.erase(player_id)

func _on_player_position_updated(player_id: int, position: Vector2, direction: Vector2, is_moving: bool, is_running: bool):
	"""Handle remote player position update"""
	if player_id in remote_players:
		var remote_player = remote_players[player_id]
		remote_player.set_position_and_direction(position, direction)
		remote_player.is_moving = is_moving
		remote_player.is_running = is_running

func _on_player_stats_changed(player_id: int):
	"""Handle player stats changed"""
	if local_player and local_player.player_id == player_id:
		print("Local player stats updated")

# Utility methods
func get_local_player() -> Player:
	"""Get the local player instance"""
	return local_player

func get_remote_player(player_id: int) -> Player:
	"""Get a remote player by ID"""
	return remote_players.get(player_id, null)

func get_all_players() -> Array:
	"""Get all players in the scene"""
	var all_players = []
	if local_player:
		all_players.append(local_player)
	for remote_player in remote_players.values():
		all_players.append(remote_player)
	return all_players

func change_district(new_district: String):
	"""Change to a different Istanbul district"""
	print("Changing district from ", current_district, " to ", new_district)
	current_district = new_district
	
	# This would load new district assets in full implementation
	# For M0, just update the reference
	
	if WorldManager:
		WorldManager.load_district(new_district)

func cleanup():
	"""Clean up the scene"""
	if local_player:
		local_player.queue_free()
		local_player = null
	
	for remote_player in remote_players.values():
		remote_player.queue_free()
	remote_players.clear()
	
	print("MainGameScene cleaned up")
