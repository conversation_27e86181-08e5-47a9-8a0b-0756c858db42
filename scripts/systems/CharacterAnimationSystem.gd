extends Node
class_name CharacterAnimationSystem

## CharacterAnimationSystem - Manages character animations for Ottoman Empire MMORPG
## Handles sprite sheets, animation states, and directional animations

# Animation state enum
enum AnimationState {
	IDLE,
	WALKING,
	RUNNING,
	ATTACKING,
	CASTING,
	HURT,
	DYING,
	DEAD
}

# Animation configuration
const ANIMATION_FRAME_SIZE = Vector2(64, 64)  # 64x64 character sprites
const ANIMATION_FPS = 8  # 8 frames per second for character animations
const SPRITE_SHEET_COLUMNS = 8  # 8 directions per row
const SPRITE_SHEET_ROWS = 8     # Different animation types per column

# Animation frame counts for each animation type
const ANIMATION_FRAME_COUNTS = {
	"idle": 4,
	"walk": 6,
	"run": 6,
	"attack": 4,
	"scimitar_attack": 5,
	"gunpowder_shot": 3,
	"cast": 4,
	"hurt": 2,
	"death": 6
}

# Direction mapping for sprite sheet (matches MovementSystem directions)
const SPRITE_SHEET_DIRECTION_MAP = {
	MovementSystem.Direction.DOWN: 0,
	MovementSystem.Direction.DOWN_RIGHT: 1,
	MovementSystem.Direction.RIGHT: 2,
	MovementSystem.Direction.UP_RIGHT: 3,
	MovementSystem.Direction.UP: 4,
	MovementSystem.Direction.UP_LEFT: 5,
	MovementSystem.Direction.LEFT: 6,
	MovementSystem.Direction.DOWN_LEFT: 7
}

static func setup_character_animations(animation_player: AnimationPlayer, sprite: Sprite2D, character_class: String = "janissary"):
	"""Set up all animations for a character"""
	if not animation_player or not sprite:
		print("Error: AnimationPlayer or Sprite2D is null")
		return
	
	# Load appropriate sprite sheet for character class
	var sprite_sheet_path = "res://assets/characters/" + character_class + "_spritesheet.png"
	
	# For M0 prototype, we'll create placeholder animations
	_create_basic_animations(animation_player, sprite)
	
	print("Character animations set up for class: ", character_class)

static func _create_basic_animations(animation_player: AnimationPlayer, sprite: Sprite2D):
	"""Create basic animations for character"""
	var animation_library = AnimationLibrary.new()
	
	# Create idle animation
	var idle_anim = _create_idle_animation(sprite)
	animation_library.add_animation("idle", idle_anim)
	
	# Create walk animation
	var walk_anim = _create_walk_animation(sprite)
	animation_library.add_animation("walk", walk_anim)
	
	# Create run animation
	var run_anim = _create_run_animation(sprite)
	animation_library.add_animation("run", run_anim)
	
	# Create attack animation
	var attack_anim = _create_attack_animation(sprite)
	animation_library.add_animation("attack", attack_anim)
	
	# Create scimitar attack animation (Janissary specific)
	var scimitar_anim = _create_scimitar_attack_animation(sprite)
	animation_library.add_animation("scimitar_attack", scimitar_anim)
	
	# Create hurt animation
	var hurt_anim = _create_hurt_animation(sprite)
	animation_library.add_animation("hurt", hurt_anim)
	
	# Create death animation
	var death_anim = _create_death_animation(sprite)
	animation_library.add_animation("death", death_anim)
	
	# Add the library to the animation player
	animation_player.add_animation_library("default", animation_library)

static func _create_idle_animation(sprite: Sprite2D) -> Animation:
	"""Create idle animation"""
	var animation = Animation.new()
	animation.length = 2.0  # 2 second loop
	animation.loop_mode = Animation.LOOP_LINEAR
	
	# Add sprite frame track
	var track_index = animation.add_track(Animation.TYPE_VALUE)
	animation.track_set_path(track_index, NodePath(".:frame"))
	
	# Simple breathing animation (frame 0-1-0-1)
	animation.track_insert_key(track_index, 0.0, 0)
	animation.track_insert_key(track_index, 0.5, 1)
	animation.track_insert_key(track_index, 1.0, 0)
	animation.track_insert_key(track_index, 1.5, 1)
	animation.track_insert_key(track_index, 2.0, 0)
	
	return animation

static func _create_walk_animation(sprite: Sprite2D) -> Animation:
	"""Create walking animation"""
	var animation = Animation.new()
	animation.length = 0.75  # 0.75 seconds per cycle
	animation.loop_mode = Animation.LOOP_LINEAR
	
	var track_index = animation.add_track(Animation.TYPE_VALUE)
	animation.track_set_path(track_index, NodePath(".:frame"))
	
	# Walking cycle (6 frames)
	var frame_time = animation.length / 6.0
	for i in range(6):
		animation.track_insert_key(track_index, i * frame_time, i + 2)  # Frames 2-7 for walking
	
	return animation

static func _create_run_animation(sprite: Sprite2D) -> Animation:
	"""Create running animation"""
	var animation = Animation.new()
	animation.length = 0.5  # Faster than walking
	animation.loop_mode = Animation.LOOP_LINEAR
	
	var track_index = animation.add_track(Animation.TYPE_VALUE)
	animation.track_set_path(track_index, NodePath(".:frame"))
	
	# Running cycle (6 frames, same as walk but faster)
	var frame_time = animation.length / 6.0
	for i in range(6):
		animation.track_insert_key(track_index, i * frame_time, i + 8)  # Frames 8-13 for running
	
	return animation

static func _create_attack_animation(sprite: Sprite2D) -> Animation:
	"""Create basic attack animation"""
	var animation = Animation.new()
	animation.length = 0.5  # 0.5 second attack
	animation.loop_mode = Animation.LOOP_NONE
	
	var track_index = animation.add_track(Animation.TYPE_VALUE)
	animation.track_set_path(track_index, NodePath(".:frame"))
	
	# Attack frames
	animation.track_insert_key(track_index, 0.0, 14)   # Wind up
	animation.track_insert_key(track_index, 0.1, 15)   # Strike
	animation.track_insert_key(track_index, 0.2, 16)   # Impact
	animation.track_insert_key(track_index, 0.4, 17)   # Recovery
	animation.track_insert_key(track_index, 0.5, 0)    # Return to idle
	
	return animation

static func _create_scimitar_attack_animation(sprite: Sprite2D) -> Animation:
	"""Create Janissary scimitar attack animation"""
	var animation = Animation.new()
	animation.length = 0.6  # Slightly longer for scimitar technique
	animation.loop_mode = Animation.LOOP_NONE
	
	var track_index = animation.add_track(Animation.TYPE_VALUE)
	animation.track_set_path(track_index, NodePath(".:frame"))
	
	# Scimitar attack sequence
	animation.track_insert_key(track_index, 0.0, 18)   # Stance
	animation.track_insert_key(track_index, 0.1, 19)   # Draw back
	animation.track_insert_key(track_index, 0.2, 20)   # Slash
	animation.track_insert_key(track_index, 0.3, 21)   # Follow through
	animation.track_insert_key(track_index, 0.5, 22)   # Recovery
	animation.track_insert_key(track_index, 0.6, 0)    # Return to idle
	
	return animation

static func _create_hurt_animation(sprite: Sprite2D) -> Animation:
	"""Create hurt/damage animation"""
	var animation = Animation.new()
	animation.length = 0.3
	animation.loop_mode = Animation.LOOP_NONE
	
	var track_index = animation.add_track(Animation.TYPE_VALUE)
	animation.track_set_path(track_index, NodePath(".:frame"))
	
	# Hurt animation
	animation.track_insert_key(track_index, 0.0, 23)   # Hit reaction
	animation.track_insert_key(track_index, 0.15, 24)  # Stagger
	animation.track_insert_key(track_index, 0.3, 0)    # Return to idle
	
	# Add color flash effect
	var color_track = animation.add_track(Animation.TYPE_VALUE)
	animation.track_set_path(color_track, NodePath(".:modulate"))
	animation.track_insert_key(color_track, 0.0, Color.RED)
	animation.track_insert_key(color_track, 0.1, Color.WHITE)
	
	return animation

static func _create_death_animation(sprite: Sprite2D) -> Animation:
	"""Create death animation"""
	var animation = Animation.new()
	animation.length = 2.0
	animation.loop_mode = Animation.LOOP_NONE
	
	var track_index = animation.add_track(Animation.TYPE_VALUE)
	animation.track_set_path(track_index, NodePath(".:frame"))
	
	# Death sequence
	var frame_time = animation.length / 6.0
	for i in range(6):
		animation.track_insert_key(track_index, i * frame_time, i + 25)  # Frames 25-30 for death
	
	# Fade out effect
	var alpha_track = animation.add_track(Animation.TYPE_VALUE)
	animation.track_set_path(alpha_track, NodePath(".:modulate:a"))
	animation.track_insert_key(alpha_track, 0.0, 1.0)
	animation.track_insert_key(alpha_track, 1.5, 1.0)
	animation.track_insert_key(alpha_track, 2.0, 0.3)  # Semi-transparent when dead
	
	return animation

static func play_directional_animation(animation_player: AnimationPlayer, base_animation: String, direction: MovementSystem.Direction):
	"""Play animation with directional variant if available"""
	if not animation_player:
		return
	
	# Try directional animation first
	var directional_name = base_animation + "_" + MovementSystem.get_direction_name(direction)
	
	if animation_player.has_animation(directional_name):
		animation_player.play(directional_name)
	elif animation_player.has_animation(base_animation):
		animation_player.play(base_animation)
	else:
		print("Warning: Animation not found: ", base_animation)

static func create_placeholder_sprite_texture() -> ImageTexture:
	"""Create a placeholder sprite texture for testing"""
	var image = Image.create(512, 512, false, Image.FORMAT_RGBA8)
	
	# Fill with a simple pattern
	for y in range(512):
		for x in range(512):
			var color = Color.WHITE
			
			# Create a simple character silhouette pattern
			var center_x = x % 64 - 32
			var center_y = y % 64 - 32
			var distance = sqrt(center_x * center_x + center_y * center_y)
			
			if distance < 20:
				color = Color.BLUE  # Body
			elif distance < 25:
				color = Color.DARK_BLUE  # Outline
			else:
				color = Color.TRANSPARENT
			
			image.set_pixel(x, y, color)
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	return texture

static func setup_sprite_sheet(sprite: Sprite2D, character_class: String = "janissary"):
	"""Set up sprite sheet for character"""
	# For M0 prototype, use placeholder texture
	sprite.texture = create_placeholder_sprite_texture()
	
	# Configure sprite for sprite sheet animation
	sprite.hframes = 8  # 8 columns (directions)
	sprite.vframes = 8  # 8 rows (animation types)
	sprite.frame = 0    # Start with first frame
	
	print("Sprite sheet configured for ", character_class)

static func get_animation_state_from_character(character: BaseCharacter) -> AnimationState:
	"""Determine animation state from character properties"""
	if character.health <= 0:
		return AnimationState.DEAD
	
	if character.is_moving:
		if character.is_running:
			return AnimationState.RUNNING
		else:
			return AnimationState.WALKING
	
	return AnimationState.IDLE

static func update_character_animation(character: BaseCharacter, animation_player: AnimationPlayer):
	"""Update character animation based on current state"""
	if not animation_player:
		return
	
	var state = get_animation_state_from_character(character)
	var direction = MovementSystem.get_direction_from_vector(character.facing_direction)
	
	match state:
		AnimationState.IDLE:
			play_directional_animation(animation_player, "idle", direction)
		AnimationState.WALKING:
			play_directional_animation(animation_player, "walk", direction)
		AnimationState.RUNNING:
			play_directional_animation(animation_player, "run", direction)
		AnimationState.DEAD:
			if animation_player.current_animation != "death":
				animation_player.play("death")
		_:
			# Keep current animation for other states
			pass
