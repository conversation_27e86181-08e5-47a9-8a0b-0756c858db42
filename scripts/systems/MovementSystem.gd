extends Node
class_name MovementSystem

## MovementSystem - Handles 8-directional movement for Ottoman Empire characters
## Provides smooth movement, direction handling, and animation coordination

# 8-directional movement constants
enum Direction {
	DOWN = 0,      # South
	DOWN_RIGHT = 1, # Southeast  
	RIGHT = 2,     # East
	UP_RIGHT = 3,  # Northeast
	UP = 4,        # North
	UP_LEFT = 5,   # Northwest
	LEFT = 6,      # West
	DOWN_LEFT = 7  # Southwest
}

# Direction vectors for 8-directional movement
const DIRECTION_VECTORS = [
	Vector2(0, 1),    # DOWN
	Vector2(1, 1),    # DOWN_RIGHT
	Vector2(1, 0),    # RIGHT
	Vector2(1, -1),   # UP_RIGHT
	Vector2(0, -1),   # UP
	Vector2(-1, -1),  # UP_LEFT
	Vector2(-1, 0),   # LEFT
	Vector2(-1, 1)    # DOWN_LEFT
]

# Direction names for animation and debugging
const DIRECTION_NAMES = [
	"down", "down_right", "right", "up_right",
	"up", "up_left", "left", "down_left"
]

static func get_direction_from_vector(direction_vector: Vector2) -> Direction:
	"""Convert a direction vector to a Direction enum"""
	if direction_vector.length() < 0.1:
		return Direction.DOWN  # Default direction
	
	# Normalize the vector
	var normalized = direction_vector.normalized()
	
	# Calculate angle in degrees (0-360)
	var angle = rad_to_deg(normalized.angle())
	if angle < 0:
		angle += 360
	
	# Convert angle to 8-directional index
	# Each direction covers 45 degrees, starting from East (0°)
	var direction_index = int((angle + 22.5) / 45.0) % 8
	
	# Adjust for our coordinate system (Down = 0)
	# Godot's angle() returns 0 for right, we want 0 for down
	var adjusted_index = (direction_index + 6) % 8
	
	return adjusted_index as Direction

static func get_vector_from_direction(direction: Direction) -> Vector2:
	"""Get the normalized vector for a direction"""
	return DIRECTION_VECTORS[direction].normalized()

static func get_direction_name(direction: Direction) -> String:
	"""Get the string name for a direction"""
	return DIRECTION_NAMES[direction]

static func get_opposite_direction(direction: Direction) -> Direction:
	"""Get the opposite direction"""
	return ((direction + 4) % 8) as Direction

static func is_diagonal_direction(direction: Direction) -> bool:
	"""Check if direction is diagonal"""
	return direction % 2 == 1

static func get_cardinal_directions(direction: Direction) -> Array[Direction]:
	"""Get the two cardinal directions that make up a diagonal direction"""
	if not is_diagonal_direction(direction):
		return [direction]  # Already cardinal
	
	match direction:
		Direction.DOWN_RIGHT:
			return [Direction.DOWN, Direction.RIGHT]
		Direction.UP_RIGHT:
			return [Direction.UP, Direction.RIGHT]
		Direction.UP_LEFT:
			return [Direction.UP, Direction.LEFT]
		Direction.DOWN_LEFT:
			return [Direction.DOWN, Direction.LEFT]
		_:
			return [direction]

static func calculate_movement_input() -> Vector2:
	"""Calculate movement input from player actions"""
	var input_vector = Vector2.ZERO
	
	# Get input from action map
	if Input.is_action_pressed("move_up"):
		input_vector.y -= 1
	if Input.is_action_pressed("move_down"):
		input_vector.y += 1
	if Input.is_action_pressed("move_left"):
		input_vector.x -= 1
	if Input.is_action_pressed("move_right"):
		input_vector.x += 1
	
	# Normalize diagonal movement to prevent faster diagonal movement
	if input_vector.length() > 0:
		input_vector = input_vector.normalized()
	
	return input_vector

static func apply_movement_smoothing(current_velocity: Vector2, target_velocity: Vector2, acceleration: float, deceleration: float, delta: float) -> Vector2:
	"""Apply smooth acceleration and deceleration to movement"""
	if target_velocity.length() > 0:
		# Accelerating towards target velocity
		return current_velocity.move_toward(target_velocity, acceleration * delta)
	else:
		# Decelerating to stop
		return current_velocity.move_toward(Vector2.ZERO, deceleration * delta)

# Animation helper functions
static func get_animation_name_for_direction(direction: Direction, is_moving: bool, is_running: bool) -> String:
	"""Get the appropriate animation name for direction and movement state"""
	var base_name = ""
	
	if not is_moving:
		base_name = "idle"
	elif is_running:
		base_name = "run"
	else:
		base_name = "walk"
	
	# Add direction suffix for directional animations
	var direction_name = get_direction_name(direction)
	return base_name + "_" + direction_name

static func get_sprite_flip_for_direction(direction: Direction) -> Dictionary:
	"""Get sprite flip settings for a direction"""
	var result = {"flip_h": false, "flip_v": false}
	
	match direction:
		Direction.LEFT, Direction.UP_LEFT, Direction.DOWN_LEFT:
			result.flip_h = true
		Direction.RIGHT, Direction.UP_RIGHT, Direction.DOWN_RIGHT:
			result.flip_h = false
		_:
			# For up/down, maintain current flip state or use default
			pass
	
	return result

# Movement validation and collision helpers
static func validate_movement_direction(character: CharacterBody2D, direction: Vector2, distance: float) -> Vector2:
	"""Validate movement direction against collisions and return adjusted direction"""
	if direction.length() < 0.1:
		return Vector2.ZERO
	
	# Use Godot's built-in collision detection
	var space_state = character.get_world_2d().direct_space_state
	var query = PhysicsRayQueryParameters2D.create(
		character.global_position,
		character.global_position + direction * distance
	)
	query.collision_mask = character.collision_mask
	query.exclude = [character]
	
	var result = space_state.intersect_ray(query)
	
	if result.is_empty():
		return direction  # No collision, movement is valid
	
	# Try to slide along the collision surface
	var collision_normal = result.get("normal", Vector2.ZERO)
	if collision_normal.length() > 0:
		return direction.slide(collision_normal).normalized()
	
	return Vector2.ZERO  # Can't move in this direction

# Utility functions for Ottoman Empire specific movement
static func apply_stance_movement_modifier(base_speed: float, stance: String) -> float:
	"""Apply movement speed modifiers based on Ottoman combat stance"""
	match stance:
		"disciplined":
			return base_speed  # No modifier for disciplined stance
		"charge":
			return base_speed * 1.2  # 20% speed boost for Sipahi charge
		"whirling":
			return base_speed * 1.1  # 10% speed boost for Sufi whirling
		_:
			return base_speed

static func calculate_stamina_cost(is_running: bool, is_in_combat: bool, stance: String) -> float:
	"""Calculate stamina cost per second based on movement and combat state"""
	var base_cost = 0.0
	
	if is_running:
		base_cost = 30.0  # Base running cost
		
		# Stance modifiers
		match stance:
			"charge":
				base_cost *= 1.5  # Sipahi charge costs more stamina
			"whirling":
				base_cost *= 0.8  # Sufi mystical efficiency
			_:
				pass  # No modifier for disciplined
	
	if is_in_combat:
		base_cost += 10.0  # Additional cost when in combat
	
	return base_cost

# Debug and visualization helpers
static func debug_draw_movement_direction(character: Node2D, direction: Vector2, color: Color = Color.GREEN):
	"""Draw movement direction for debugging (requires custom drawing)"""
	if direction.length() > 0.1:
		var start_pos = character.global_position
		var end_pos = start_pos + direction * 50
		
		# This would require a custom drawing system or debug overlay
		print("Movement direction: ", direction, " (", get_direction_name(get_direction_from_vector(direction)), ")")

static func get_movement_debug_info(character: CharacterBody2D, direction: Vector2) -> Dictionary:
	"""Get debug information about character movement"""
	return {
		"position": character.global_position,
		"velocity": character.velocity,
		"direction": direction,
		"direction_enum": get_direction_from_vector(direction),
		"direction_name": get_direction_name(get_direction_from_vector(direction)),
		"speed": character.velocity.length(),
		"is_moving": direction.length() > 0.1
	}
