extends Node

## UIManager - Manages user interface for Ottoman Empire game
## Handles menu systems, HUD, inventory, and UI state management

signal ui_state_changed(new_state: String)
signal menu_action(action: String)

enum UIState {
	HIDDEN,
	MAIN_MENU,
	CHARACTER_CREATION,
	GAME_HUD,
	INVENTORY,
	PAUSE_MENU,
	SETTINGS,
	LOADING,
	DISCONNECTED
}

var current_ui_state: UIState = UIState.HIDDEN
var ui_scenes: Dictionary = {}
var active_ui_nodes: Dictionary = {}

func _ready():
	print("UIManager initialized for Ottoman Empire interface")
	_initialize_ui_scenes()

func _initialize_ui_scenes():
	"""Initialize UI scene paths"""
	ui_scenes = {
		UIState.MAIN_MENU: "res://scenes/ui/MainMenu.tscn",
		UIState.CHARACTER_CREATION: "res://scenes/ui/CharacterCreation.tscn",
		UIState.GAME_HUD: "res://scenes/ui/GameHUD.tscn",
		UIState.INVENTORY: "res://scenes/ui/InventoryPanel.tscn",
		UIState.PAUSE_MENU: "res://scenes/ui/PauseMenu.tscn",
		UIState.SETTINGS: "res://scenes/ui/SettingsMenu.tscn",
		UIState.LOADING: "res://scenes/ui/LoadingScreen.tscn",
		UIState.DISCONNECTED: "res://scenes/ui/DisconnectedScreen.tscn"
	}

func show_main_menu():
	"""Show the main menu"""
	_change_ui_state(UIState.MAIN_MENU)

func show_character_creation():
	"""Show character creation screen"""
	_change_ui_state(UIState.CHARACTER_CREATION)

func show_game_ui():
	"""Show the game HUD"""
	_change_ui_state(UIState.GAME_HUD)

func show_inventory():
	"""Show inventory panel"""
	_change_ui_state(UIState.INVENTORY)

func show_pause_menu():
	"""Show pause menu"""
	_change_ui_state(UIState.PAUSE_MENU)

func show_settings_menu():
	"""Show settings menu"""
	_change_ui_state(UIState.SETTINGS)

func show_loading_screen():
	"""Show loading screen"""
	_change_ui_state(UIState.LOADING)

func show_disconnected_screen():
	"""Show disconnected screen"""
	_change_ui_state(UIState.DISCONNECTED)

func hide_all_ui():
	"""Hide all UI elements"""
	_change_ui_state(UIState.HIDDEN)

func _change_ui_state(new_state: UIState):
	"""Change the current UI state"""
	if current_ui_state == new_state:
		return

	current_ui_state = new_state
	print("UI state changed to: ", UIState.keys()[new_state])
	ui_state_changed.emit(UIState.keys()[new_state])

func display_chat_message(player_id: int, message: String, channel: String):
	"""Display chat message in game UI"""
	var player_name = "Unknown"
	if NetworkManager:
		player_name = NetworkManager.get_player_name(player_id)

	print("[", channel.to_upper(), "] ", player_name, ": ", message)

func update_player_stats_display(player_id: int):
	"""Update player stats in UI"""
	var player_data = PlayerManager.get_player_data(player_id) if PlayerManager else null
	if player_data:
		print("Updating UI for player: ", player_data.player_name, " (Level ", player_data.level, ")")

func show_notification(title: String, message: String, duration: float = 3.0):
	"""Show a notification to the player"""
	print("NOTIFICATION: ", title, " - ", message)

func toggle_inventory():
	"""Toggle inventory panel"""
	if current_ui_state == UIState.INVENTORY:
		show_game_ui()
	else:
		show_inventory()

# Utility functions
func get_current_ui_state() -> String:
	"""Get current UI state as string"""
	return UIState.keys()[current_ui_state]

func is_game_ui_active() -> bool:
	"""Check if game UI is currently active"""
	return current_ui_state == UIState.GAME_HUD
