extends Node

## GameManager - Core game state and scene management for Moon-Break: Chronicles of the Ottoman Empire
## Handles game initialization, state transitions, and global game settings

signal game_state_changed(new_state: GameState)
signal scene_transition_started(scene_name: String)
signal scene_transition_completed(scene_name: String)

enum GameState {
	MENU,
	LOADING,
	IN_GAME,
	PAUSED,
	SETTINGS,
	DISCONNECTED
}

var current_state: GameState = GameState.MENU
var previous_state: GameState = GameState.MENU
var is_multiplayer: bool = false
var player_count: int = 1
var target_fps: int = 60

# Ottoman Empire game settings
var game_version: String = "0.1.0-M0"
var game_title: String = "Moon-Break: Chronicles of the Ottoman Empire"
var current_district: String = "sultanahmet"  # Starting district

func _ready():
	print("GameManager initialized - ", game_title, " v", game_version)

	# Set up game settings
	Engine.max_fps = target_fps

	# Connect to other managers
	_connect_to_managers()

	# Initialize game state
	change_state(GameState.MENU)

func _connect_to_managers():
	"""Connect to other core managers"""
	# Wait for other singletons to be ready
	await get_tree().process_frame

	if NetworkManager:
		NetworkManager.connection_established.connect(_on_network_connected)
		NetworkManager.connection_lost.connect(_on_network_disconnected)

	if WorldManager:
		WorldManager.district_loaded.connect(_on_district_loaded)
		WorldManager.district_unloaded.connect(_on_district_unloaded)

func change_state(new_state: GameState):
	"""Change the current game state"""
	if current_state == new_state:
		return

	previous_state = current_state
	current_state = new_state

	print("Game state changed: ", GameState.keys()[previous_state], " -> ", GameState.keys()[current_state])
	game_state_changed.emit(new_state)

	# Handle state-specific logic
	match current_state:
		GameState.MENU:
			_handle_menu_state()
		GameState.LOADING:
			_handle_loading_state()
		GameState.IN_GAME:
			_handle_in_game_state()
		GameState.PAUSED:
			_handle_paused_state()
		GameState.SETTINGS:
			_handle_settings_state()
		GameState.DISCONNECTED:
			_handle_disconnected_state()

func _handle_menu_state():
	"""Handle menu state logic"""
	get_tree().paused = false
	if UIManager:
		UIManager.show_main_menu()

func _handle_loading_state():
	"""Handle loading state logic"""
	if UIManager:
		UIManager.show_loading_screen()

func _handle_in_game_state():
	"""Handle in-game state logic"""
	get_tree().paused = false
	if UIManager:
		UIManager.show_game_ui()

func _handle_paused_state():
	"""Handle paused state logic"""
	get_tree().paused = true
	if UIManager:
		UIManager.show_pause_menu()

func _handle_settings_state():
	"""Handle settings state logic"""
	if UIManager:
		UIManager.show_settings_menu()

func _handle_disconnected_state():
	"""Handle disconnected state logic"""
	get_tree().paused = false
	if UIManager:
		UIManager.show_disconnected_screen()

func start_single_player():
	"""Start single player game"""
	is_multiplayer = false
	player_count = 1
	change_state(GameState.LOADING)

	# Load starting district (Sultanahmet)
	if WorldManager:
		WorldManager.load_district("sultanahmet")

func start_multiplayer():
	"""Start multiplayer game"""
	is_multiplayer = true
	change_state(GameState.LOADING)

	# Initialize network connection
	if NetworkManager:
		NetworkManager.start_client()

func pause_game():
	"""Pause the game"""
	if current_state == GameState.IN_GAME:
		change_state(GameState.PAUSED)

func resume_game():
	"""Resume the game"""
	if current_state == GameState.PAUSED:
		change_state(GameState.IN_GAME)

func quit_to_menu():
	"""Quit to main menu"""
	if NetworkManager and is_multiplayer:
		NetworkManager.disconnect_from_server()

	if WorldManager:
		WorldManager.unload_current_district()

	change_state(GameState.MENU)

func quit_game():
	"""Quit the entire game"""
	print("Quitting Moon-Break: Chronicles of the Ottoman Empire")
	get_tree().quit()

# Network event handlers
func _on_network_connected():
	"""Handle successful network connection"""
	print("Connected to Ottoman Empire server")
	if current_state == GameState.LOADING:
		# Load starting district for multiplayer
		if WorldManager:
			WorldManager.load_district("sultanahmet")

func _on_network_disconnected():
	"""Handle network disconnection"""
	print("Disconnected from Ottoman Empire server")
	change_state(GameState.DISCONNECTED)

# World event handlers
func _on_district_loaded(district_name: String):
	"""Handle district loading completion"""
	print("District loaded: ", district_name)
	current_district = district_name

	if current_state == GameState.LOADING:
		change_state(GameState.IN_GAME)

func _on_district_unloaded(district_name: String):
	"""Handle district unloading"""
	print("District unloaded: ", district_name)

# Utility functions
func get_current_state_name() -> String:
	"""Get the current state as a string"""
	return GameState.keys()[current_state]

func is_game_active() -> bool:
	"""Check if the game is actively running"""
	return current_state == GameState.IN_GAME

func is_game_paused() -> bool:
	"""Check if the game is paused"""
	return current_state == GameState.PAUSED
