extends Node

## PlayerManager - Manages player data, characters, and inventory for Ottoman Empire
## Handles local and remote player state, character progression, and equipment

signal player_stats_changed(player_id: int)
signal player_level_up(player_id: int, new_level: int)
signal inventory_changed(player_id: int)
signal equipment_changed(player_id: int, slot: String)

# Player character classes
enum CharacterClass {
	JANISSARY,
	SIPAHI,
	SUFI_MYSTIC
}

# Player data structure
class PlayerData:
	var player_id: int
	var player_name: String
	var character_class: CharacterClass
	var level: int = 1
	var experience: int = 0
	var health: int = 100
	var max_health: int = 100
	var stamina: int = 100
	var max_stamina: int = 100
	var akce_coins: int = 0  # Ottoman currency

	# Character customization
	var hairstyle: int = 0  # 0-5 (6 hairstyles)
	var face_scar: int = 0  # 0-3 (4 face scars)
	var skin_tone: int = 0  # 0-2 (3 skin tones)

	# Combat stats
	var attack_power: int = 10
	var defense: int = 5
	var current_stance: String = "disciplined"  # Janissary default

	# Position and movement
	var position: Vector2 = Vector2.ZERO
	var direction: Vector2 = Vector2.ZERO
	var is_moving: bool = false
	var is_running: bool = false

	# Equipment slots
	var equipment: Dictionary = {
		"helmet": null,
		"pauldrons": null,
		"chest": null,
		"bracers": null,
		"belt": null,
		"boots": null,
		"weapon": null,
		"accessory_1": null,
		"accessory_2": null
	}

	# Inventory (10x6 grid = 60 slots)
	var inventory: Array = []

	func _init(id: int, name: String, char_class: CharacterClass):
		player_id = id
		player_name = name
		character_class = char_class
		_initialize_inventory()
		_set_class_defaults()

	func _initialize_inventory():
		inventory.resize(60)  # 10x6 grid
		for i in range(60):
			inventory[i] = null

	func _set_class_defaults():
		match character_class:
			CharacterClass.JANISSARY:
				max_health = 120
				max_stamina = 100
				attack_power = 12
				defense = 8
				current_stance = "disciplined"
			CharacterClass.SIPAHI:
				max_health = 100
				max_stamina = 120
				attack_power = 10
				defense = 6
				current_stance = "charge"
			CharacterClass.SUFI_MYSTIC:
				max_health = 80
				max_stamina = 140
				attack_power = 8
				defense = 4
				current_stance = "whirling"

		health = max_health
		stamina = max_stamina

# Player management
var local_player: PlayerData
var remote_players: Dictionary = {}
var local_player_id: int = -1

func _ready():
	print("PlayerManager initialized for Ottoman Empire")

func create_local_player(player_name: String, character_class: CharacterClass) -> PlayerData:
	"""Create the local player character"""
	local_player_id = multiplayer.get_unique_id() if multiplayer.has_multiplayer_peer() else 1
	local_player = PlayerData.new(local_player_id, player_name, character_class)

	print("Created local player: ", player_name, " (", CharacterClass.keys()[character_class], ")")
	return local_player

func add_remote_player(player_id: int, player_name: String, character_class: CharacterClass) -> PlayerData:
	"""Add a remote player"""
	var remote_player = PlayerData.new(player_id, player_name, character_class)
	remote_players[player_id] = remote_player

	print("Added remote player: ", player_name, " (ID: ", player_id, ")")
	return remote_player

func remove_remote_player(player_id: int):
	"""Remove a remote player"""
	if player_id in remote_players:
		var player_name = remote_players[player_id].player_name
		remote_players.erase(player_id)
		print("Removed remote player: ", player_name, " (ID: ", player_id, ")")

func get_player_data(player_id: int) -> PlayerData:
	"""Get player data by ID"""
	if player_id == local_player_id and local_player:
		return local_player
	elif player_id in remote_players:
		return remote_players[player_id]
	return null

func update_local_player_position(position: Vector2, direction: Vector2, is_moving: bool, is_running: bool):
	"""Update local player position and movement state"""
	if not local_player:
		return

	local_player.position = position
	local_player.direction = direction
	local_player.is_moving = is_moving
	local_player.is_running = is_running

	# Send to network if multiplayer
	if NetworkManager and NetworkManager.is_multiplayer_active():
		NetworkManager.send_player_position(position, direction)

func update_remote_player_position(player_id: int, position: Vector2, direction: Vector2):
	"""Update remote player position from network"""
	var player_data = get_player_data(player_id)
	if player_data:
		player_data.position = position
		player_data.direction = direction
		player_data.is_moving = direction.length() > 0.1

func change_player_stance(player_id: int, new_stance: String):
	"""Change player combat stance"""
	var player_data = get_player_data(player_id)
	if player_data:
		player_data.current_stance = new_stance
		print("Player ", player_data.player_name, " changed stance to: ", new_stance)

func add_experience(player_id: int, exp_amount: int):
	"""Add experience to player"""
	var player_data = get_player_data(player_id)
	if not player_data:
		return

	player_data.experience += exp_amount

	# Check for level up
	var exp_needed = _calculate_exp_for_level(player_data.level + 1)
	if player_data.experience >= exp_needed:
		_level_up_player(player_data)

	player_stats_changed.emit(player_id)

func _level_up_player(player_data: PlayerData):
	"""Handle player level up"""
	player_data.level += 1

	# Increase stats based on class
	match player_data.character_class:
		CharacterClass.JANISSARY:
			player_data.max_health += 8
			player_data.max_stamina += 5
			player_data.attack_power += 2
			player_data.defense += 2
		CharacterClass.SIPAHI:
			player_data.max_health += 6
			player_data.max_stamina += 8
			player_data.attack_power += 2
			player_data.defense += 1
		CharacterClass.SUFI_MYSTIC:
			player_data.max_health += 4
			player_data.max_stamina += 10
			player_data.attack_power += 1
			player_data.defense += 1

	# Restore health and stamina on level up
	player_data.health = player_data.max_health
	player_data.stamina = player_data.max_stamina

	print("Player ", player_data.player_name, " reached level ", player_data.level, "!")
	player_level_up.emit(player_data.player_id, player_data.level)

func _calculate_exp_for_level(level: int) -> int:
	"""Calculate experience needed for a specific level"""
	return level * level * 100  # Simple quadratic progression

func take_damage(player_id: int, damage: int) -> bool:
	"""Apply damage to player, returns true if player died"""
	var player_data = get_player_data(player_id)
	if not player_data:
		return false

	# Apply defense reduction
	var actual_damage = max(1, damage - player_data.defense)
	player_data.health = max(0, player_data.health - actual_damage)

	player_stats_changed.emit(player_id)

	# Check if player died
	if player_data.health <= 0:
		_handle_player_death(player_data)
		return true

	return false

func heal_player(player_id: int, heal_amount: int):
	"""Heal player"""
	var player_data = get_player_data(player_id)
	if not player_data:
		return

	player_data.health = min(player_data.max_health, player_data.health + heal_amount)
	player_stats_changed.emit(player_id)

func _handle_player_death(player_data: PlayerData):
	"""Handle player death"""
	print("Player ", player_data.player_name, " has fallen in battle!")

	# Respawn logic (simplified for M0)
	player_data.health = player_data.max_health
	player_data.stamina = player_data.max_stamina

	# Move to safe respawn point (Sultanahmet fountain)
	player_data.position = Vector2(640, 360)  # Center of starting area

func add_item_to_inventory(player_id: int, item_data: Dictionary) -> bool:
	"""Add item to player inventory"""
	var player_data = get_player_data(player_id)
	if not player_data:
		return false

	# Find first empty slot
	for i in range(player_data.inventory.size()):
		if player_data.inventory[i] == null:
			player_data.inventory[i] = item_data
			inventory_changed.emit(player_id)
			return true

	print("Inventory full for player: ", player_data.player_name)
	return false

func remove_item_from_inventory(player_id: int, slot_index: int) -> Dictionary:
	"""Remove item from inventory slot"""
	var player_data = get_player_data(player_id)
	if not player_data or slot_index < 0 or slot_index >= player_data.inventory.size():
		return {}

	var item = player_data.inventory[slot_index]
	player_data.inventory[slot_index] = null
	inventory_changed.emit(player_id)

	return item if item else {}

func equip_item(player_id: int, item_data: Dictionary, equipment_slot: String) -> bool:
	"""Equip item to equipment slot"""
	var player_data = get_player_data(player_id)
	if not player_data or not equipment_slot in player_data.equipment:
		return false

	# Unequip current item if any
	var old_item = player_data.equipment[equipment_slot]
	if old_item:
		add_item_to_inventory(player_id, old_item)

	# Equip new item
	player_data.equipment[equipment_slot] = item_data
	equipment_changed.emit(player_id, equipment_slot)

	# Apply item stats (simplified)
	_apply_equipment_stats(player_data)

	return true

func _apply_equipment_stats(player_data: PlayerData):
	"""Recalculate player stats based on equipment"""
	# Reset to base stats
	_set_base_stats_for_level(player_data)

	# Apply equipment bonuses
	for slot in player_data.equipment:
		var item = player_data.equipment[slot]
		if item and "stats" in item:
			var stats = item["stats"]
			if "attack_power" in stats:
				player_data.attack_power += stats["attack_power"]
			if "defense" in stats:
				player_data.defense += stats["defense"]
			if "max_health" in stats:
				player_data.max_health += stats["max_health"]
			if "max_stamina" in stats:
				player_data.max_stamina += stats["max_stamina"]

func _set_base_stats_for_level(player_data: PlayerData):
	"""Set base stats for player level"""
	# This would normally be more complex, simplified for M0
	var level_bonus = (player_data.level - 1)

	match player_data.character_class:
		CharacterClass.JANISSARY:
			player_data.max_health = 120 + (level_bonus * 8)
			player_data.max_stamina = 100 + (level_bonus * 5)
			player_data.attack_power = 12 + (level_bonus * 2)
			player_data.defense = 8 + (level_bonus * 2)
		CharacterClass.SIPAHI:
			player_data.max_health = 100 + (level_bonus * 6)
			player_data.max_stamina = 120 + (level_bonus * 8)
			player_data.attack_power = 10 + (level_bonus * 2)
			player_data.defense = 6 + (level_bonus * 1)
		CharacterClass.SUFI_MYSTIC:
			player_data.max_health = 80 + (level_bonus * 4)
			player_data.max_stamina = 140 + (level_bonus * 10)
			player_data.attack_power = 8 + (level_bonus * 1)
			player_data.defense = 4 + (level_bonus * 1)

func add_akce_coins(player_id: int, amount: int):
	"""Add akçe coins to player"""
	var player_data = get_player_data(player_id)
	if player_data:
		player_data.akce_coins += amount
		player_stats_changed.emit(player_id)

func spend_akce_coins(player_id: int, amount: int) -> bool:
	"""Spend akçe coins, returns true if successful"""
	var player_data = get_player_data(player_id)
	if not player_data or player_data.akce_coins < amount:
		return false

	player_data.akce_coins -= amount
	player_stats_changed.emit(player_id)
	return true

# Utility functions
func get_local_player() -> PlayerData:
	"""Get local player data"""
	return local_player

func get_all_players() -> Array:
	"""Get all player data (local + remote)"""
	var all_players = []
	if local_player:
		all_players.append(local_player)
	for player_id in remote_players:
		all_players.append(remote_players[player_id])
	return all_players

func get_class_name(character_class: CharacterClass) -> String:
	"""Get character class name"""
	return CharacterClass.keys()[character_class]
