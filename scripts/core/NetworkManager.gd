extends Node

## NetworkManager - Handles multiplayer networking for Moon-Break: Chronicles of the Ottoman Empire
## Manages client-server communication, player synchronization, and network events

signal connection_established()
signal connection_lost()
signal player_joined(player_id: int, player_name: String)
signal player_left(player_id: int)
signal server_message_received(message: Dictionary)

const DEFAULT_PORT = 7000
const MAX_PLAYERS = 4  # M0 prototype supports 4 players
const TICK_RATE = 20   # 20Hz server tick rate

var multiplayer_peer: ENetMultiplayerPeer
var is_server: bool = false
var is_connected: bool = false
var server_address: String = "127.0.0.1"
var server_port: int = DEFAULT_PORT

# Player management
var connected_players: Dictionary = {}
var local_player_id: int = -1

# Network synchronization
var network_tick: int = 0
var last_sync_time: float = 0.0
var sync_interval: float = 1.0 / TICK_RATE

func _ready():
	print("NetworkManager initialized for Ottoman Empire multiplayer")

	# Set up multiplayer signals
	multiplayer.peer_connected.connect(_on_peer_connected)
	multiplayer.peer_disconnected.connect(_on_peer_disconnected)
	multiplayer.connected_to_server.connect(_on_connected_to_server)
	multiplayer.connection_failed.connect(_on_connection_failed)
	multiplayer.server_disconnected.connect(_on_server_disconnected)

func _process(delta):
	"""Handle network tick updates"""
	if is_server and is_connected:
		last_sync_time += delta
		if last_sync_time >= sync_interval:
			_network_tick()
			last_sync_time = 0.0

func start_server(port: int = DEFAULT_PORT) -> bool:
	"""Start the game server"""
	print("Starting Ottoman Empire server on port: ", port)

	multiplayer_peer = ENetMultiplayerPeer.new()
	var error = multiplayer_peer.create_server(port, MAX_PLAYERS)

	if error != OK:
		print("Failed to start server: ", error)
		return false

	multiplayer.multiplayer_peer = multiplayer_peer
	is_server = true
	is_connected = true
	server_port = port

	print("Ottoman Empire server started successfully")
	return true

func start_client(address: String = "127.0.0.1", port: int = DEFAULT_PORT) -> bool:
	"""Connect to game server as client"""
	print("Connecting to Ottoman Empire server: ", address, ":", port)

	multiplayer_peer = ENetMultiplayerPeer.new()
	var error = multiplayer_peer.create_client(address, port)

	if error != OK:
		print("Failed to create client: ", error)
		return false

	multiplayer.multiplayer_peer = multiplayer_peer
	server_address = address
	server_port = port

	return true

func disconnect_from_server():
	"""Disconnect from the server"""
	if multiplayer_peer:
		multiplayer_peer.close()
		multiplayer_peer = null

	multiplayer.multiplayer_peer = null
	is_connected = false
	is_server = false
	connected_players.clear()

	print("Disconnected from Ottoman Empire server")

func send_player_position(position: Vector2, direction: Vector2):
	"""Send player position update to server/clients"""
	if not is_connected:
		return

	var data = {
		"type": "player_position",
		"player_id": multiplayer.get_unique_id(),
		"position": position,
		"direction": direction,
		"timestamp": Time.get_unix_time_from_system()
	}

	if is_server:
		_broadcast_to_clients.rpc(data)
	else:
		_send_to_server.rpc_id(1, data)

func send_combat_action(action_type: String, target_position: Vector2, stance: String):
	"""Send combat action to server/clients"""
	if not is_connected:
		return

	var data = {
		"type": "combat_action",
		"player_id": multiplayer.get_unique_id(),
		"action": action_type,
		"target_position": target_position,
		"stance": stance,
		"timestamp": Time.get_unix_time_from_system()
	}

	if is_server:
		_broadcast_to_clients.rpc(data)
	else:
		_send_to_server.rpc_id(1, data)

func send_chat_message(message: String, channel: String = "general"):
	"""Send chat message"""
	if not is_connected:
		return

	var data = {
		"type": "chat_message",
		"player_id": multiplayer.get_unique_id(),
		"message": message,
		"channel": channel,
		"timestamp": Time.get_unix_time_from_system()
	}

	if is_server:
		_broadcast_to_clients.rpc(data)
	else:
		_send_to_server.rpc_id(1, data)

@rpc("any_peer", "reliable")
func _send_to_server(data: Dictionary):
	"""Receive data from client (server-side)"""
	if not is_server:
		return

	# Add sender ID
	data["sender_id"] = multiplayer.get_remote_sender_id()

	# Process server-side logic
	_process_network_message(data)

	# Broadcast to other clients
	_broadcast_to_clients.rpc(data)

@rpc("authority", "reliable")
func _broadcast_to_clients(data: Dictionary):
	"""Receive broadcast from server (client-side)"""
	if is_server:
		return

	_process_network_message(data)

func _process_network_message(data: Dictionary):
	"""Process incoming network message"""
	match data.get("type", ""):
		"player_position":
			_handle_player_position(data)
		"combat_action":
			_handle_combat_action(data)
		"chat_message":
			_handle_chat_message(data)
		"player_joined":
			_handle_player_joined(data)
		"player_left":
			_handle_player_left(data)

func _handle_player_position(data: Dictionary):
	"""Handle player position update"""
	var player_id = data.get("player_id", -1)
	var position = data.get("position", Vector2.ZERO)
	var direction = data.get("direction", Vector2.ZERO)

	# Update player position in PlayerManager
	if PlayerManager:
		PlayerManager.update_remote_player_position(player_id, position, direction)

func _handle_combat_action(data: Dictionary):
	"""Handle combat action"""
	var player_id = data.get("player_id", -1)
	var action = data.get("action", "")
	var target_position = data.get("target_position", Vector2.ZERO)
	var stance = data.get("stance", "")

	# Process combat action in CombatManager
	if CombatManager:
		CombatManager.process_remote_combat_action(player_id, action, target_position, stance)

func _handle_chat_message(data: Dictionary):
	"""Handle chat message"""
	var player_id = data.get("player_id", -1)
	var message = data.get("message", "")
	var channel = data.get("channel", "general")

	# Display chat message in UI
	if UIManager:
		UIManager.display_chat_message(player_id, message, channel)

func _handle_player_joined(data: Dictionary):
	"""Handle player joining"""
	var player_id = data.get("player_id", -1)
	var player_name = data.get("player_name", "Unknown Janissary")

	connected_players[player_id] = {
		"name": player_name,
		"connected_time": Time.get_unix_time_from_system()
	}

	player_joined.emit(player_id, player_name)
	print("Player joined Ottoman Empire: ", player_name, " (ID: ", player_id, ")")

func _handle_player_left(data: Dictionary):
	"""Handle player leaving"""
	var player_id = data.get("player_id", -1)

	if player_id in connected_players:
		var player_name = connected_players[player_id].get("name", "Unknown")
		connected_players.erase(player_id)
		player_left.emit(player_id)
		print("Player left Ottoman Empire: ", player_name, " (ID: ", player_id, ")")

func _network_tick():
	"""Server network tick - process game state updates"""
	network_tick += 1

	# Sync world state, combat updates, etc.
	if WorldManager:
		WorldManager.network_sync_tick(network_tick)

	if CombatManager:
		CombatManager.network_sync_tick(network_tick)

# Multiplayer event handlers
func _on_peer_connected(id: int):
	"""Handle peer connection"""
	print("Peer connected: ", id)

	if is_server:
		# Send welcome message to new player
		var welcome_data = {
			"type": "player_joined",
			"player_id": id,
			"player_name": "Janissary_" + str(id)
		}
		_broadcast_to_clients.rpc(welcome_data)

func _on_peer_disconnected(id: int):
	"""Handle peer disconnection"""
	print("Peer disconnected: ", id)

	if is_server:
		# Notify other players
		var leave_data = {
			"type": "player_left",
			"player_id": id
		}
		_broadcast_to_clients.rpc(leave_data)

	# Clean up player data
	if id in connected_players:
		connected_players.erase(id)

func _on_connected_to_server():
	"""Handle successful connection to server"""
	print("Successfully connected to Ottoman Empire server")
	is_connected = true
	local_player_id = multiplayer.get_unique_id()
	connection_established.emit()

func _on_connection_failed():
	"""Handle failed connection to server"""
	print("Failed to connect to Ottoman Empire server")
	is_connected = false
	connection_lost.emit()

func _on_server_disconnected():
	"""Handle server disconnection"""
	print("Disconnected from Ottoman Empire server")
	is_connected = false
	connected_players.clear()
	connection_lost.emit()

# Utility functions
func get_connected_player_count() -> int:
	"""Get number of connected players"""
	return connected_players.size()

func get_player_name(player_id: int) -> String:
	"""Get player name by ID"""
	if player_id in connected_players:
		return connected_players[player_id].get("name", "Unknown Janissary")
	return "Unknown Janissary"

func is_multiplayer_active() -> bool:
	"""Check if multiplayer is active"""
	return is_connected and (is_server or multiplayer.has_multiplayer_peer())
