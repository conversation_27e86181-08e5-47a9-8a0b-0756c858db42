extends Node

## CombatManager - Handles combat system for Ottoman Empire warriors
## Manages damage calculation, hit detection, stances, and combat effects

signal combat_action_performed(player_id: int, action: String)
signal damage_dealt(attacker_id: int, target_id: int, damage: int)
signal stance_changed(player_id: int, stance: String)

# Ottoman combat stances
enum CombatStance {
	DISCIPLINED,    # Janissary - balanced offense/defense
	CHARGE,         # Sipahi - high mobility, cavalry tactics
	WHIRLING        # Sufi Mystic - spinning attacks, mystical powers
}

# Combat action types
enum ActionType {
	LIGHT_ATTACK,
	HEAVY_ATTACK,
	BLOCK,
	DODGE,
	SPECIAL_ABILITY
}

var active_combats: Dictionary = {}
var stance_bonuses: Dictionary = {}

func _ready():
	print("CombatManager initialized for Ottoman Empire combat")
	_initialize_stance_bonuses()

func _initialize_stance_bonuses():
	"""Initialize combat stance bonuses"""
	stance_bonuses = {
		"disciplined": {
			"attack_bonus": 0,
			"defense_bonus": 5,
			"speed_bonus": 0,
			"special": "formation_bonus"
		},
		"charge": {
			"attack_bonus": 3,
			"defense_bonus": -2,
			"speed_bonus": 8,
			"special": "cavalry_charge"
		},
		"whirling": {
			"attack_bonus": 1,
			"defense_bonus": 2,
			"speed_bonus": 5,
			"special": "mystical_spin"
		}
	}

func perform_combat_action(player_id: int, action_type: ActionType, target_position: Vector2, stance: String):
	"""Perform a combat action"""
	var player_data = PlayerManager.get_player_data(player_id)
	if not player_data:
		return

	# Calculate damage based on stance and action
	var damage = _calculate_damage(player_data, action_type, stance)

	# Apply stance bonuses
	if stance in stance_bonuses:
		var bonus = stance_bonuses[stance]
		damage += bonus.get("attack_bonus", 0)

	print("Player ", player_data.player_name, " performs ", ActionType.keys()[action_type], " for ", damage, " damage")
	combat_action_performed.emit(player_id, ActionType.keys()[action_type])

func process_remote_combat_action(player_id: int, action: String, target_position: Vector2, stance: String):
	"""Process combat action from remote player"""
	print("Remote combat action: Player ", player_id, " - ", action, " in ", stance, " stance")

func change_combat_stance(player_id: int, new_stance: String):
	"""Change player combat stance"""
	if PlayerManager:
		PlayerManager.change_player_stance(player_id, new_stance)
	stance_changed.emit(player_id, new_stance)

func _calculate_damage(player_data, action_type: ActionType, stance: String) -> int:
	"""Calculate damage for combat action"""
	var base_damage = player_data.attack_power

	match action_type:
		ActionType.LIGHT_ATTACK:
			return base_damage
		ActionType.HEAVY_ATTACK:
			return int(base_damage * 1.5)
		ActionType.SPECIAL_ABILITY:
			return int(base_damage * 2.0)
		_:
			return base_damage

func network_sync_tick(tick: int):
	"""Handle network synchronization for combat"""
	# Sync combat state for multiplayer
	pass
