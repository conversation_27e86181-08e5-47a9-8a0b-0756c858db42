extends Player
class_name Janissary

## Janissary - Elite Ottoman infantry class
## Disciplined warriors with balanced combat abilities and formation tactics

signal formation_bonus_activated()
signal disciplined_stance_changed(bonus_active: bool)

# Janissary-specific properties
var formation_bonus_active: bool = false
var formation_bonus_range: float = 64.0  # Range to detect other Janissaries
var discipline_level: int = 1  # Increases with level, affects bonuses

# Janissary combat abilities
var scimitar_proficiency: int = 10  # Bonus damage with scimitars
var gunpowder_knowledge: int = 5   # Bonus with early firearms
var formation_training: int = 8    # Bonus when near allies

# Disciplined stance bonuses
var disciplined_defense_bonus: int = 5
var disciplined_accuracy_bonus: int = 3
var disciplined_stamina_efficiency: float = 0.9  # 10% less stamina consumption

func _ready():
	super._ready()
	character_name = "Janissary Warrior"
	character_class = PlayerManager.CharacterClass.JANISSARY
	
	# Set Janissary-specific stats
	_initialize_janissary_stats()
	
	# Set default stance
	current_stance = "disciplined"
	
	print("Janissary initialized: ", character_name)

func _initialize_janissary_stats():
	"""Initialize Janissary-specific statistics"""
	# Base stats for Janissary (balanced warrior)
	max_health = 120
	health = max_health
	max_stamina = 100
	stamina = max_stamina
	attack_power = 12
	defense = 8
	base_speed = 120.0
	
	# Janissary-specific modifiers
	attack_cooldown_time = 1.0  # Standard attack speed
	
	# Apply discipline bonuses
	_apply_disciplined_stance_bonuses()

func _apply_disciplined_stance_bonuses():
	"""Apply bonuses for disciplined stance"""
	if current_stance == "disciplined":
		defense += disciplined_defense_bonus
		
		# Reduce stamina consumption
		var original_consumption = MovementSystem.calculate_stamina_cost(true, false, "disciplined")
		# This is handled in the MovementSystem, but we track it here
		
		print("Disciplined stance activated - Defense bonus: +", disciplined_defense_bonus)

func _physics_process(delta):
	super._physics_process(delta)
	
	# Check for formation bonuses with nearby Janissaries
	_check_formation_bonus()

func _check_formation_bonus():
	"""Check for nearby Janissaries to activate formation bonus"""
	var nearby_janissaries = _find_nearby_janissaries()
	var should_have_bonus = nearby_janissaries.size() > 0
	
	if should_have_bonus != formation_bonus_active:
		formation_bonus_active = should_have_bonus
		_apply_formation_bonus(formation_bonus_active)

func _find_nearby_janissaries() -> Array:
	"""Find nearby Janissary characters for formation bonus"""
	var nearby_janissaries = []
	
	# Get all players from PlayerManager
	if PlayerManager:
		var all_players = PlayerManager.get_all_players()
		for player_data in all_players:
			if player_data.character_class == PlayerManager.CharacterClass.JANISSARY and player_data.player_id != player_id:
				# Calculate distance (simplified - would need actual character positions in full implementation)
				var distance = global_position.distance_to(player_data.position)
				if distance <= formation_bonus_range:
					nearby_janissaries.append(player_data)
	
	return nearby_janissaries

func _apply_formation_bonus(active: bool):
	"""Apply or remove formation bonus"""
	if active:
		# Formation bonus: +2 attack, +3 defense, +10% speed
		attack_power += 2
		defense += 3
		base_speed *= 1.1
		print("Formation bonus activated! Fighting alongside fellow Janissaries.")
		formation_bonus_activated.emit()
	else:
		# Remove formation bonus
		attack_power -= 2
		defense -= 3
		base_speed /= 1.1
		print("Formation bonus deactivated.")

func _perform_scimitar_attack():
	"""Perform Janissary scimitar attack with special techniques"""
	if not can_attack:
		return
	
	can_attack = false
	attack_cooldown = attack_cooldown_time
	
	# Calculate enhanced attack with scimitar proficiency
	var base_damage = attack_power + scimitar_proficiency
	
	# Disciplined stance accuracy bonus
	var accuracy_bonus = disciplined_accuracy_bonus if current_stance == "disciplined" else 0
	
	# Formation bonus damage
	var formation_damage = 2 if formation_bonus_active else 0
	
	var total_damage = base_damage + accuracy_bonus + formation_damage
	
	# Attack position in front of character
	var attack_position = global_position + (facing_direction * 40)  # Slightly longer reach for scimitar
	
	print("Janissary scimitar attack! Damage: ", total_damage, " at position: ", attack_position)
	
	# Emit combat action
	combat_action_performed.emit("scimitar_attack")
	
	# Send to CombatManager with enhanced damage
	if CombatManager:
		CombatManager.perform_combat_action(
			player_id,
			CombatManager.ActionType.LIGHT_ATTACK,
			attack_position,
			current_stance
		)
	
	# Play scimitar attack animation
	if animation_player:
		if animation_player.has_animation("scimitar_attack"):
			animation_player.play("scimitar_attack")
		elif animation_player.has_animation("attack"):
			animation_player.play("attack")

func perform_disciplined_formation_attack():
	"""Special Janissary formation attack - coordinated strike"""
	if not can_attack or not formation_bonus_active:
		return
	
	print("Janissary formation attack! Coordinated strike with nearby allies.")
	
	# Enhanced formation attack
	can_attack = false
	attack_cooldown = attack_cooldown_time * 0.8  # Faster recovery in formation
	
	# Calculate formation attack damage
	var formation_damage = attack_power + formation_training + scimitar_proficiency
	
	# Multiple attack positions for formation strike
	var attack_positions = [
		global_position + (facing_direction * 32),
		global_position + (facing_direction * 48),
		global_position + (facing_direction.rotated(PI/4) * 32)
	]
	
	for pos in attack_positions:
		if CombatManager:
			CombatManager.perform_combat_action(
				player_id,
				CombatManager.ActionType.SPECIAL_ABILITY,
				pos,
				current_stance
			)
	
	combat_action_performed.emit("formation_attack")

func use_gunpowder_weapon():
	"""Use early gunpowder weapon (Janissary special ability)"""
	if stamina < 30:  # Requires stamina
		print("Not enough stamina for gunpowder weapon!")
		return
	
	stamina -= 30
	stamina_changed.emit(stamina, max_stamina)
	
	# Gunpowder weapon has longer range but longer cooldown
	var gunpowder_range = 128.0
	var target_position = global_position + (facing_direction * gunpowder_range)
	
	print("Janissary fires gunpowder weapon at: ", target_position)
	
	# High damage but slow reload
	can_attack = false
	attack_cooldown = 3.0  # 3 second reload time
	
	if CombatManager:
		CombatManager.perform_combat_action(
			player_id,
			CombatManager.ActionType.SPECIAL_ABILITY,
			target_position,
			current_stance
		)
	
	combat_action_performed.emit("gunpowder_shot")
	
	# Play gunpowder animation
	if animation_player and animation_player.has_animation("gunpowder_shot"):
		animation_player.play("gunpowder_shot")

func _change_stance(new_stance: String):
	"""Override stance change to handle Janissary-specific bonuses"""
	var old_stance = current_stance
	
	# Remove old stance bonuses
	if old_stance == "disciplined":
		defense -= disciplined_defense_bonus
	
	# Apply parent stance change
	super._change_stance(new_stance)
	
	# Apply new stance bonuses
	if new_stance == "disciplined":
		_apply_disciplined_stance_bonuses()
		disciplined_stance_changed.emit(true)
	else:
		disciplined_stance_changed.emit(false)

func level_up():
	"""Handle Janissary level up with class-specific bonuses"""
	level += 1
	discipline_level += 1
	
	# Janissary level up bonuses
	max_health += 8
	max_stamina += 5
	attack_power += 2
	defense += 2
	
	# Increase proficiencies
	scimitar_proficiency += 1
	if level % 3 == 0:  # Every 3 levels
		gunpowder_knowledge += 1
		formation_training += 1
	
	# Restore health and stamina
	health = max_health
	stamina = max_stamina
	
	print("Janissary reached level ", level, "! Discipline increased to ", discipline_level)

# Override input handling to include Janissary-specific abilities
func _unhandled_input(event):
	super._unhandled_input(event)
	
	if not is_player_controlled or not can_move:
		return
	
	# Janissary-specific input handling
	if event.is_action_pressed("attack"):
		# Use scimitar attack instead of basic attack
		_perform_scimitar_attack()
	
	# Special abilities (could be mapped to additional keys)
	if event.is_action_pressed("ui_accept"):  # Space key for special ability
		if formation_bonus_active:
			perform_disciplined_formation_attack()
		else:
			use_gunpowder_weapon()

# Utility methods
func get_janissary_info() -> Dictionary:
	"""Get Janissary-specific information"""
	var base_info = get_player_info()
	base_info["discipline_level"] = discipline_level
	base_info["scimitar_proficiency"] = scimitar_proficiency
	base_info["gunpowder_knowledge"] = gunpowder_knowledge
	base_info["formation_training"] = formation_training
	base_info["formation_bonus_active"] = formation_bonus_active
	return base_info

func is_in_formation() -> bool:
	"""Check if Janissary is currently in formation with allies"""
	return formation_bonus_active
