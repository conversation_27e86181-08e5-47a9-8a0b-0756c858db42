extends BaseCharacter
class_name Player

## Player - Player-controlled character for Ottoman Empire MMORPG
## Handles input, combat actions, and player-specific functionality

signal combat_action_performed(action_type: String)
signal stance_changed(new_stance: String)

# Player-specific properties
var player_id: int = -1
var experience: int = 0
var akce_coins: int = 0

# Combat input handling
var can_attack: bool = true
var attack_cooldown: float = 0.0
var attack_cooldown_time: float = 1.0  # 1 second between attacks

# Equipment and inventory
var equipped_weapon: Dictionary = {}
var inventory: Array = []

func _ready():
	super._ready()
	character_name = "Ottoman Player"
	is_player_controlled = true
	
	# Connect to managers
	_connect_to_managers()
	
	print("Player character initialized")

func _connect_to_managers():
	"""Connect to game managers"""
	# Connect to PlayerManager signals
	if PlayerManager:
		PlayerManager.player_stats_changed.connect(_on_player_stats_changed)
		PlayerManager.equipment_changed.connect(_on_equipment_changed)

func _unhandled_input(event):
	"""Handle player input events"""
	if not is_player_controlled or not can_move:
		return
	
	# Combat input
	if event.is_action_pressed("attack") and can_attack:
		_perform_attack()
	
	# Stance switching
	if event.is_action_pressed("stance_1"):
		_change_stance("disciplined")
	elif event.is_action_pressed("stance_2"):
		_change_stance("charge")
	elif event.is_action_pressed("stance_3"):
		_change_stance("whirling")

func _process(delta):
	"""Handle per-frame updates"""
	super._process(delta)
	
	# Handle attack cooldown
	if attack_cooldown > 0:
		attack_cooldown -= delta
		if attack_cooldown <= 0:
			can_attack = true

func _perform_attack():
	"""Perform attack action"""
	if not can_attack:
		return
	
	can_attack = false
	attack_cooldown = attack_cooldown_time
	
	# Calculate attack position (in front of player)
	var attack_position = global_position + (facing_direction * 32)  # 32 pixels in front
	
	print("Player attacks at position: ", attack_position)
	
	# Emit combat action signal
	combat_action_performed.emit("light_attack")
	
	# Send to CombatManager
	if CombatManager:
		CombatManager.perform_combat_action(
			player_id, 
			CombatManager.ActionType.LIGHT_ATTACK, 
			attack_position, 
			current_stance
		)
	
	# Send to NetworkManager if multiplayer
	if NetworkManager and NetworkManager.is_multiplayer_active():
		NetworkManager.send_combat_action("light_attack", attack_position, current_stance)
	
	# Play attack animation
	if animation_player and animation_player.has_animation("attack"):
		animation_player.play("attack")

func _change_stance(new_stance: String):
	"""Change combat stance"""
	if new_stance == current_stance:
		return
	
	var old_stance = current_stance
	current_stance = new_stance
	
	print("Player changed stance from ", old_stance, " to ", new_stance)
	
	# Apply stance changes
	super.change_stance(new_stance)
	
	# Emit signal
	stance_changed.emit(new_stance)
	
	# Update PlayerManager
	if PlayerManager:
		PlayerManager.change_player_stance(player_id, new_stance)
	
	# Send to CombatManager
	if CombatManager:
		CombatManager.change_combat_stance(player_id, new_stance)

func initialize_player(player_data: PlayerManager.PlayerData):
	"""Initialize player with data from PlayerManager"""
	player_id = player_data.player_id
	character_name = player_data.player_name
	character_class = player_data.character_class
	level = player_data.level
	health = player_data.health
	max_health = player_data.max_health
	stamina = player_data.stamina
	max_stamina = player_data.max_stamina
	attack_power = player_data.attack_power
	defense = player_data.defense
	current_stance = player_data.current_stance
	akce_coins = player_data.akce_coins
	
	# Set class-specific properties
	_apply_class_properties()
	
	print("Player initialized: ", character_name, " (", PlayerManager.get_class_name(character_class), ")")

func _apply_class_properties():
	"""Apply class-specific properties"""
	match character_class:
		PlayerManager.CharacterClass.JANISSARY:
			# Janissary: Balanced warrior with disciplined stance
			base_speed = 120.0
			attack_cooldown_time = 1.0
			current_stance = "disciplined"
			
		PlayerManager.CharacterClass.SIPAHI:
			# Sipahi: Fast cavalry with charge stance
			base_speed = 140.0
			attack_cooldown_time = 0.8
			current_stance = "charge"
			
		PlayerManager.CharacterClass.SUFI_MYSTIC:
			# Sufi Mystic: Mystical warrior with whirling stance
			base_speed = 110.0
			attack_cooldown_time = 1.2
			current_stance = "whirling"

func add_experience(amount: int):
	"""Add experience to player"""
	experience += amount
	
	# Update PlayerManager
	if PlayerManager:
		PlayerManager.add_experience(player_id, amount)

func add_akce(amount: int):
	"""Add akçe coins to player"""
	akce_coins += amount
	
	# Update PlayerManager
	if PlayerManager:
		PlayerManager.add_akce_coins(player_id, amount)

func spend_akce(amount: int) -> bool:
	"""Spend akçe coins"""
	if PlayerManager:
		return PlayerManager.spend_akce_coins(player_id, amount)
	return false

# Signal handlers
func _on_player_stats_changed(changed_player_id: int):
	"""Handle player stats changed signal"""
	if changed_player_id == player_id:
		# Update local stats from PlayerManager
		var player_data = PlayerManager.get_player_data(player_id)
		if player_data:
			health = player_data.health
			max_health = player_data.max_health
			stamina = player_data.stamina
			max_stamina = player_data.max_stamina
			level = player_data.level
			experience = player_data.experience
			akce_coins = player_data.akce_coins

func _on_equipment_changed(changed_player_id: int, slot: String):
	"""Handle equipment changed signal"""
	if changed_player_id == player_id:
		print("Equipment changed in slot: ", slot)
		# Update visual representation of equipment
		_update_equipment_visuals()

func _update_equipment_visuals():
	"""Update character visuals based on equipment"""
	# This would update sprite textures based on equipped items
	# For M0 prototype, we'll keep it simple
	pass

# Utility methods
func get_player_info() -> Dictionary:
	"""Get comprehensive player information"""
	var base_data = get_character_data()
	base_data["player_id"] = player_id
	base_data["experience"] = experience
	base_data["akce_coins"] = akce_coins
	base_data["equipped_weapon"] = equipped_weapon
	return base_data

func respawn(spawn_position: Vector2):
	"""Respawn player at given position"""
	global_position = spawn_position
	health = max_health
	stamina = max_stamina
	can_move = true
	
	# Play respawn animation if available
	if animation_player and animation_player.has_animation("respawn"):
		animation_player.play("respawn")
	else:
		animation_player.play("idle")
	
	print("Player respawned at: ", spawn_position)
