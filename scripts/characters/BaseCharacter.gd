extends CharacterBody2D
class_name BaseCharacter

## BaseCharacter - Foundation for all characters in Ottoman Empire
## Handles movement, collision, animation, and basic character functionality

signal health_changed(new_health: int, max_health: int)
signal stamina_changed(new_stamina: int, max_stamina: int)
signal direction_changed(new_direction: Vector2)
signal movement_state_changed(is_moving: bool, is_running: bool)

# Character stats
var character_name: String = "Ottoman Warrior"
var character_class: PlayerManager.CharacterClass = PlayerManager.CharacterClass.JANISSARY
var level: int = 1
var health: int = 100
var max_health: int = 100
var stamina: int = 100
var max_stamina: int = 100

# Movement properties
var base_speed: float = 120.0  # Base movement speed (pixels/second)
var run_speed_multiplier: float = 1.8  # Running speed multiplier
var current_speed: float = 120.0
var movement_direction: Vector2 = Vector2.ZERO
var facing_direction: Vector2 = Vector2.DOWN  # Default facing down

# Movement state
var is_moving: bool = false
var is_running: bool = false
var can_move: bool = true
var is_in_combat: bool = false

# Combat properties
var attack_power: int = 10
var defense: int = 5
var current_stance: String = "disciplined"

# Animation and visual
@onready var sprite: Sprite2D = $Sprite2D
@onready var animation_player: AnimationPlayer = $AnimationPlayer
@onready var collision_shape: CollisionShape2D = $CollisionShape2D

# Input handling (only for player-controlled characters)
var is_player_controlled: bool = false

func _ready():
	print("BaseCharacter initialized: ", character_name)
	_setup_character()

func _setup_character():
	"""Initialize character properties"""
	# Set up collision layer (Players = layer 1)
	collision_layer = 1
	collision_mask = 4  # Collide with Environment (layer 3)

	# Initialize facing direction sprite
	_update_sprite_direction()

func _physics_process(delta):
	"""Handle physics and movement"""
	if not can_move:
		velocity = Vector2.ZERO
		move_and_slide()
		return

	# Handle movement input (if player controlled)
	if is_player_controlled:
		_handle_movement_input()

	# Apply movement
	_apply_movement(delta)

	# Update movement state
	_update_movement_state()

	# Regenerate stamina when not running
	if not is_running:
		_regenerate_stamina(delta)

func _handle_movement_input():
	"""Handle player input for movement using MovementSystem"""
	# Use MovementSystem for consistent 8-directional input
	var input_direction = MovementSystem.calculate_movement_input()

	# Update facing direction if moving
	if input_direction.length() > 0:
		facing_direction = input_direction
		direction_changed.emit(facing_direction)

	movement_direction = input_direction

	# Handle running with stamina check
	is_running = Input.is_action_pressed("run") and input_direction.length() > 0 and stamina > 0

func _apply_movement(delta: float):
	"""Apply movement to character using MovementSystem"""
	if movement_direction.length() > 0:
		# Calculate speed with stance modifiers
		current_speed = MovementSystem.apply_stance_movement_modifier(base_speed, current_stance)

		if is_running and stamina > 0:
			current_speed *= run_speed_multiplier
			_consume_stamina(delta)

		# Validate movement direction against collisions
		var validated_direction = MovementSystem.validate_movement_direction(self, movement_direction, 32.0)

		# Apply movement with validated direction
		velocity = validated_direction * current_speed
	else:
		# Apply smooth deceleration
		velocity = MovementSystem.apply_movement_smoothing(velocity, Vector2.ZERO, 0, base_speed * 3, delta)

	# Move the character
	move_and_slide()

	# Update sprite direction
	if movement_direction.length() > 0:
		_update_sprite_direction()

func _update_movement_state():
	"""Update movement state and emit signals"""
	var was_moving = is_moving
	is_moving = velocity.length() > 10.0  # Small threshold to avoid jitter

	if was_moving != is_moving:
		movement_state_changed.emit(is_moving, is_running)
		_update_animation()

	# Update PlayerManager if this is the local player
	if is_player_controlled and PlayerManager:
		PlayerManager.update_local_player_position(global_position, facing_direction, is_moving, is_running)

func _update_sprite_direction():
	"""Update sprite based on facing direction using MovementSystem"""
	if not sprite:
		return

	# Get direction enum from MovementSystem
	var direction = MovementSystem.get_direction_from_vector(facing_direction)

	# Apply sprite flipping based on direction
	var flip_settings = MovementSystem.get_sprite_flip_for_direction(direction)
	sprite.flip_h = flip_settings.flip_h
	sprite.flip_v = flip_settings.flip_v

func _update_animation():
	"""Update character animation based on state using MovementSystem"""
	if not animation_player:
		return

	# Get current direction
	var direction = MovementSystem.get_direction_from_vector(facing_direction)

	# Get appropriate animation name
	var animation_name = MovementSystem.get_animation_name_for_direction(direction, is_moving, is_running)

	# Fallback to simple animations if directional animations don't exist
	if not animation_player.has_animation(animation_name):
		if is_moving:
			animation_name = "run" if is_running else "walk"
		else:
			animation_name = "idle"

	# Play the animation if it exists
	if animation_player.has_animation(animation_name):
		animation_player.play(animation_name)

func _consume_stamina(delta: float):
	"""Consume stamina while running using MovementSystem calculations"""
	var stamina_cost = MovementSystem.calculate_stamina_cost(is_running, is_in_combat, current_stance) * delta
	stamina = max(0, stamina - int(stamina_cost))
	stamina_changed.emit(stamina, max_stamina)

	# Stop running if out of stamina
	if stamina <= 0:
		is_running = false

func _regenerate_stamina(delta: float):
	"""Regenerate stamina when not running"""
	if stamina < max_stamina:
		var regen_rate = 20.0 * delta  # 20 stamina per second regeneration
		stamina = min(max_stamina, stamina + int(regen_rate))
		stamina_changed.emit(stamina, max_stamina)

# Combat methods
func take_damage(damage: int, attacker: BaseCharacter = null) -> bool:
	"""Take damage and return true if character died"""
	var actual_damage = max(1, damage - defense)
	health = max(0, health - actual_damage)
	health_changed.emit(health, max_health)

	print(character_name, " takes ", actual_damage, " damage (", health, "/", max_health, " HP)")

	if health <= 0:
		_handle_death()
		return true

	return false

func heal(amount: int):
	"""Heal the character"""
	health = min(max_health, health + amount)
	health_changed.emit(health, max_health)

func _handle_death():
	"""Handle character death"""
	print(character_name, " has fallen!")
	can_move = false

	# Play death animation if available
	if animation_player and animation_player.has_animation("death"):
		animation_player.play("death")

func set_player_controlled(controlled: bool):
	"""Set whether this character is player controlled"""
	is_player_controlled = controlled

	if controlled:
		# Connect to input events
		set_process_unhandled_input(true)
	else:
		set_process_unhandled_input(false)

func change_stance(new_stance: String):
	"""Change combat stance"""
	current_stance = new_stance
	print(character_name, " changed stance to: ", new_stance)

	# Apply stance bonuses (simplified for base class)
	match new_stance:
		"disciplined":
			defense += 2
		"charge":
			current_speed *= 1.2
		"whirling":
			# Special mystical effects would go here
			pass

# Utility methods
func get_character_data() -> Dictionary:
	"""Get character data as dictionary"""
	return {
		"name": character_name,
		"class": character_class,
		"level": level,
		"health": health,
		"max_health": max_health,
		"stamina": stamina,
		"max_stamina": max_stamina,
		"position": global_position,
		"facing_direction": facing_direction,
		"is_moving": is_moving,
		"is_running": is_running,
		"stance": current_stance
	}

func set_position_and_direction(pos: Vector2, direction: Vector2):
	"""Set character position and facing direction (for network sync)"""
	global_position = pos
	facing_direction = direction
	_update_sprite_direction()
